#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动充电车调度优化系统
Mobile Charging Vehicle Scheduling Optimization System

该系统实现了基于收益最大化和运输成本最小化的多目标优化调度算法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from geopy.distance import geodesic
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class Station:
    """场站信息数据类"""
    name: str
    device_count: int
    longitude: float
    latitude: float
    is_open: bool
    address: str
    
@dataclass
class SchedulingResult:
    """调度结果数据类"""
    from_station: str
    to_station: str
    device_count: int
    expected_revenue_increase: float
    transport_cost: float
    net_benefit: float
    distance_km: float
    
class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.stations_df = None
        self.devices_df = None
        self.orders_df = None
        self.processed_data = {}
        
    def load_data(self) -> None:
        """加载所有数据文件"""
        print("正在加载数据文件...")
        
        # 加载场站坐标数据
        self.stations_df = pd.read_csv('final_stations_107.csv')
        print(f"加载场站数据: {len(self.stations_df)} 个场站")
        
        # 加载设备数据
        self.devices_df = pd.read_csv('station_devices.csv')
        print(f"加载设备数据: {len(self.devices_df)} 个场站设备记录")
        
        # 加载订单数据
        orders_files = [
            'orders/agg_217-330.csv',
        ]
        
        orders_list = []
        for file in orders_files:
            try:
                df = pd.read_csv(file)
                orders_list.append(df)
                print(f"加载订单文件 {file}: {len(df)} 条记录")
            except FileNotFoundError:
                print(f"警告: 文件 {file} 未找到")
                
        if orders_list:
            self.orders_df = pd.concat(orders_list, ignore_index=True)
            print(f"总订单数据: {len(self.orders_df)} 条记录")
        
    def clean_and_process_data(self) -> None:
        """清洗和预处理数据"""
        print("\n正在清洗和预处理数据...")
        
        # 处理场站数据
        self.stations_df = self.stations_df.dropna(subset=['名称', '高德_经度', '高德_纬度'])
        self.stations_df['设备数量'] = pd.to_numeric(self.stations_df['设备数量'], errors='coerce').fillna(0)
        
        # 处理设备数据 - 计算每个场站的实际设备数量
        device_counts = {}
        for _, row in self.devices_df.iterrows():
            station_name = row['station_name']
            pile_nos = str(row['pile_no']).split(',')
            device_counts[station_name] = len([p for p in pile_nos if p.strip()])
            
        # 更新场站数据中的设备数量
        self.stations_df['实际设备数量'] = self.stations_df['名称'].map(device_counts).fillna(0)
        
        # 处理订单数据
        if self.orders_df is not None:
            self.orders_df['date'] = pd.to_datetime(self.orders_df['date'])
            self.orders_df['daily_revenue'] = pd.to_numeric(self.orders_df['daily_revenue'], errors='coerce')
            self.orders_df = self.orders_df.dropna(subset=['daily_revenue'])
            
        print("数据清洗完成")
        
    def get_station_summary(self) -> pd.DataFrame:
        """获取场站汇总信息"""
        summary = self.stations_df.copy()
        
        # 计算每个场站的历史收益统计
        if self.orders_df is not None:
            revenue_stats = self.orders_df.groupby('station_name')['daily_revenue'].agg([
                'mean', 'std', 'sum', 'count'
            ]).round(2)
            revenue_stats.columns = ['平均日收益', '收益标准差', '总收益', '营业天数']
            
            # 计算设备使用率指标
            summary = summary.merge(
                revenue_stats, 
                left_on='名称', 
                right_index=True, 
                how='left'
            )
            
            # 计算每设备平均收益
            summary['每设备日均收益'] = (
                summary['平均日收益'] / summary['实际设备数量']
            ).round(2)
            
        return summary
        
    def analyze_data_quality(self) -> Dict:
        """分析数据质量"""
        quality_report = {
            'stations': {
                'total_count': len(self.stations_df),
                'missing_coordinates': self.stations_df[['高德_经度', '高德_纬度']].isnull().sum().sum(),
                'zero_devices': (self.stations_df['实际设备数量'] == 0).sum(),
                'open_stations': (self.stations_df['是否开放。0：否；1：是。'] == 1).sum()
            }
        }
        
        if self.orders_df is not None:
            quality_report['orders'] = {
                'total_records': len(self.orders_df),
                'unique_stations': self.orders_df['station_name'].nunique(),
                'date_range': f"{self.orders_df['date'].min()} 到 {self.orders_df['date'].max()}",
                'missing_revenue': self.orders_df['daily_revenue'].isnull().sum()
            }
            
        return quality_report

class RevenuePredictor:
    """收益预测器"""
    
    def __init__(self, orders_df: pd.DataFrame):
        self.orders_df = orders_df
        self.station_models = {}
        
    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备特征数据"""
        df = df.copy()
        df['month'] = df['date'].dt.month
        df['day_of_week'] = df['date'].dt.dayofweek
        df['is_weekend'] = df['day_of_week'].isin([5, 6])
        df['season'] = df['month'].map({
            12: 'winter', 1: 'winter', 2: 'winter',
            3: 'spring', 4: 'spring', 5: 'spring',
            6: 'summer', 7: 'summer', 8: 'summer',
            9: 'autumn', 10: 'autumn', 11: 'autumn'
        })
        return df
        
    def predict_station_revenue(self, station_name: str, days_ahead: int = 30) -> Dict:
        """预测特定场站的收益"""
        station_data = self.orders_df[self.orders_df['station_name'] == station_name].copy()
        
        if len(station_data) < 10:  # 数据不足
            return {
                'predicted_daily_avg': 0,
                'confidence': 'low',
                'trend': 'unknown'
            }
            
        station_data = self.prepare_features(station_data)
        
        # 简单的统计预测模型
        weekend_avg = station_data[station_data['is_weekend']]['daily_revenue'].mean()
        weekday_avg = station_data[~station_data['is_weekend']]['daily_revenue'].mean()
        overall_avg = station_data['daily_revenue'].mean()
        
        # 计算趋势
        recent_data = station_data.tail(14)  # 最近两周
        earlier_data = station_data.head(14)  # 最早两周
        
        if len(recent_data) > 0 and len(earlier_data) > 0:
            recent_avg = recent_data['daily_revenue'].mean()
            earlier_avg = earlier_data['daily_revenue'].mean()
            trend = 'increasing' if recent_avg > earlier_avg else 'decreasing'
        else:
            trend = 'stable'
            
        return {
            'predicted_daily_avg': overall_avg,
            'weekend_avg': weekend_avg,
            'weekday_avg': weekday_avg,
            'confidence': 'medium' if len(station_data) > 30 else 'low',
            'trend': trend,
            'data_points': len(station_data)
        }

class DistanceCalculator:
    """距离计算器"""

    def __init__(self, stations_df: pd.DataFrame):
        self.stations_df = stations_df
        self.distance_matrix = {}

    def calculate_distance(self, station1: str, station2: str) -> float:
        """计算两个场站之间的距离（公里）"""
        if station1 == station2:
            return 0.0

        # 检查缓存
        key = tuple(sorted([station1, station2]))
        if key in self.distance_matrix:
            return self.distance_matrix[key]

        # 获取坐标
        s1_data = self.stations_df[self.stations_df['名称'] == station1]
        s2_data = self.stations_df[self.stations_df['名称'] == station2]

        if s1_data.empty or s2_data.empty:
            return float('inf')

        coord1 = (s1_data.iloc[0]['高德_纬度'], s1_data.iloc[0]['高德_经度'])
        coord2 = (s2_data.iloc[0]['高德_纬度'], s2_data.iloc[0]['高德_经度'])

        distance = geodesic(coord1, coord2).kilometers
        self.distance_matrix[key] = distance

        return distance

    def calculate_transport_cost(self, distance_km: float, device_count: int) -> float:
        """计算运输成本"""
        # 运输成本模型：基础费用 + 距离费用 + 设备数量费用
        base_cost = 200  # 基础出车费用
        distance_cost = distance_km * 3  # 每公里3元
        device_cost = device_count * 50  # 每台设备50元装卸费

        return base_cost + distance_cost + device_cost

class UtilizationAnalyzer:
    """设备使用率分析器"""

    def __init__(self, stations_df: pd.DataFrame, orders_df: pd.DataFrame):
        self.stations_df = stations_df
        self.orders_df = orders_df

    def calculate_utilization_metrics(self) -> pd.DataFrame:
        """计算设备使用率指标"""
        # 计算每个场站的收益统计
        revenue_stats = self.orders_df.groupby('station_name')['daily_revenue'].agg([
            'mean', 'std', 'count', 'sum'
        ]).round(2)

        # 合并场站信息
        utilization_df = self.stations_df.merge(
            revenue_stats,
            left_on='名称',
            right_index=True,
            how='left'
        )

        # 计算使用率指标
        utilization_df['每设备日均收益'] = (
            utilization_df['mean'] / utilization_df['实际设备数量']
        ).round(2)

        # 定义使用率等级
        utilization_df['使用率等级'] = pd.cut(
            utilization_df['每设备日均收益'].fillna(0),
            bins=[-np.inf, 10, 30, 60, np.inf],
            labels=['低', '中', '高', '极高']
        )

        # 计算供需不平衡指标
        median_revenue_per_device = utilization_df['每设备日均收益'].median()
        utilization_df['供需状态'] = utilization_df['每设备日均收益'].apply(
            lambda x: '供过于求' if x < median_revenue_per_device * 0.7 else
                     '供不应求' if x > median_revenue_per_device * 1.3 else '平衡'
        )

        return utilization_df

    def identify_optimization_opportunities(self, utilization_df: pd.DataFrame) -> List[Dict]:
        """识别优化机会"""
        opportunities = []

        # 找出供过于求的场站（设备利用率低）
        oversupply = utilization_df[
            (utilization_df['供需状态'] == '供过于求') &
            (utilization_df['实际设备数量'] > 1)
        ].copy()

        # 找出供不应求的场站（设备利用率高）
        undersupply = utilization_df[
            utilization_df['供需状态'] == '供不应求'
        ].copy()

        for _, over_station in oversupply.iterrows():
            for _, under_station in undersupply.iterrows():
                if over_station['名称'] != under_station['名称']:
                    opportunities.append({
                        'from_station': over_station['名称'],
                        'to_station': under_station['名称'],
                        'from_utilization': over_station['每设备日均收益'],
                        'to_utilization': under_station['每设备日均收益'],
                        'potential_devices': min(over_station['实际设备数量'] - 1, 3),  # 最多调3台
                        'priority': under_station['每设备日均收益'] - over_station['每设备日均收益']
                    })

        # 按优先级排序
        opportunities.sort(key=lambda x: x['priority'], reverse=True)

        return opportunities

class SchedulingOptimizer:
    """调度优化器"""

    def __init__(self, data_processor: DataProcessor):
        self.data_processor = data_processor
        self.revenue_predictor = RevenuePredictor(data_processor.orders_df)
        self.distance_calculator = DistanceCalculator(data_processor.stations_df)
        self.utilization_analyzer = UtilizationAnalyzer(
            data_processor.stations_df,
            data_processor.orders_df
        )

    def optimize_scheduling(self, max_distance_km: float = 200,
                          min_net_benefit: float = 100) -> List[SchedulingResult]:
        """执行调度优化"""
        print("正在执行调度优化...")

        # 分析设备使用率
        utilization_df = self.utilization_analyzer.calculate_utilization_metrics()

        # 识别优化机会
        opportunities = self.utilization_analyzer.identify_optimization_opportunities(utilization_df)

        scheduling_results = []

        for opp in opportunities[:20]:  # 只考虑前20个机会
            from_station = opp['from_station']
            to_station = opp['to_station']
            device_count = opp['potential_devices']

            # 计算距离和运输成本
            distance = self.distance_calculator.calculate_distance(from_station, to_station)

            if distance > max_distance_km:
                continue

            transport_cost = self.distance_calculator.calculate_transport_cost(distance, device_count)

            # 预测收益增长
            from_prediction = self.revenue_predictor.predict_station_revenue(from_station)
            to_prediction = self.revenue_predictor.predict_station_revenue(to_station)

            # 计算预期收益增长（简化模型）
            daily_revenue_increase = (
                to_prediction['predicted_daily_avg'] * device_count * 0.3 -  # 目标站增收
                from_prediction['predicted_daily_avg'] * device_count * 0.1   # 源站减收
            )

            # 计算30天的净收益
            monthly_revenue_increase = daily_revenue_increase * 30
            net_benefit = monthly_revenue_increase - transport_cost

            if net_benefit > min_net_benefit:
                result = SchedulingResult(
                    from_station=from_station,
                    to_station=to_station,
                    device_count=device_count,
                    expected_revenue_increase=monthly_revenue_increase,
                    transport_cost=transport_cost,
                    net_benefit=net_benefit,
                    distance_km=distance
                )
                scheduling_results.append(result)

        # 按净收益排序
        scheduling_results.sort(key=lambda x: x.net_benefit, reverse=True)

        return scheduling_results

    def generate_report(self, results: List[SchedulingResult]) -> str:
        """生成调度报告"""
        if not results:
            return "未找到有效的调度优化方案。"

        report = "=== 移动充电车调度优化报告 ===\n\n"
        report += f"共找到 {len(results)} 个优化方案\n\n"

        total_net_benefit = sum(r.net_benefit for r in results)
        total_transport_cost = sum(r.transport_cost for r in results)
        total_revenue_increase = sum(r.expected_revenue_increase for r in results)

        report += f"总预期净收益: ¥{total_net_benefit:,.2f}\n"
        report += f"总运输成本: ¥{total_transport_cost:,.2f}\n"
        report += f"总预期收益增长: ¥{total_revenue_increase:,.2f}\n\n"

        report += "前10个推荐方案:\n"
        report += "-" * 80 + "\n"

        for i, result in enumerate(results[:10], 1):
            report += f"{i}. {result.from_station} → {result.to_station}\n"
            report += f"   调度设备: {result.device_count} 台\n"
            report += f"   距离: {result.distance_km:.1f} km\n"
            report += f"   运输成本: ¥{result.transport_cost:,.2f}\n"
            report += f"   预期月收益增长: ¥{result.expected_revenue_increase:,.2f}\n"
            report += f"   净收益: ¥{result.net_benefit:,.2f}\n"
            report += "-" * 40 + "\n"

        return report

class Visualizer:
    """可视化器"""

    def __init__(self, data_processor: DataProcessor):
        self.data_processor = data_processor

    def plot_station_distribution(self, utilization_df: pd.DataFrame) -> None:
        """绘制场站分布图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 设备数量分布
        ax1.hist(utilization_df['实际设备数量'], bins=20, alpha=0.7, color='skyblue')
        ax1.set_xlabel('设备数量')
        ax1.set_ylabel('场站数量')
        ax1.set_title('场站设备数量分布')
        ax1.grid(True, alpha=0.3)

        # 收益分布
        revenue_data = utilization_df['mean'].dropna()
        ax2.hist(revenue_data, bins=20, alpha=0.7, color='lightgreen')
        ax2.set_xlabel('平均日收益 (元)')
        ax2.set_ylabel('场站数量')
        ax2.set_title('场站平均日收益分布')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('station_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_utilization_analysis(self, utilization_df: pd.DataFrame) -> None:
        """绘制使用率分析图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 每设备收益 vs 设备数量
        scatter_data = utilization_df.dropna(subset=['每设备日均收益'])
        ax1.scatter(scatter_data['实际设备数量'], scatter_data['每设备日均收益'],
                   alpha=0.6, s=50)
        ax1.set_xlabel('设备数量')
        ax1.set_ylabel('每设备日均收益 (元)')
        ax1.set_title('设备数量 vs 每设备收益')
        ax1.grid(True, alpha=0.3)

        # 使用率等级分布
        utilization_counts = utilization_df['使用率等级'].value_counts()
        ax2.pie(utilization_counts.values, labels=utilization_counts.index, autopct='%1.1f%%')
        ax2.set_title('设备使用率等级分布')

        # 供需状态分布
        supply_demand_counts = utilization_df['供需状态'].value_counts()
        ax3.bar(supply_demand_counts.index, supply_demand_counts.values,
                color=['red', 'green', 'orange'])
        ax3.set_xlabel('供需状态')
        ax3.set_ylabel('场站数量')
        ax3.set_title('场站供需状态分布')
        ax3.grid(True, alpha=0.3)

        # 收益趋势（按设备数量分组）
        device_groups = utilization_df.groupby(pd.cut(utilization_df['实际设备数量'],
                                                     bins=5))['mean'].mean()
        ax4.plot(range(len(device_groups)), device_groups.values, marker='o')
        ax4.set_xlabel('设备数量分组')
        ax4.set_ylabel('平均日收益 (元)')
        ax4.set_title('不同设备数量分组的平均收益')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('utilization_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def plot_scheduling_results(self, results: List[SchedulingResult]) -> None:
        """绘制调度结果图"""
        if not results:
            print("没有调度结果可以可视化")
            return

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 净收益分布
        net_benefits = [r.net_benefit for r in results]
        ax1.hist(net_benefits, bins=15, alpha=0.7, color='gold')
        ax1.set_xlabel('净收益 (元)')
        ax1.set_ylabel('方案数量')
        ax1.set_title('调度方案净收益分布')
        ax1.grid(True, alpha=0.3)

        # 距离 vs 净收益
        distances = [r.distance_km for r in results]
        ax2.scatter(distances, net_benefits, alpha=0.6, s=50, color='purple')
        ax2.set_xlabel('距离 (km)')
        ax2.set_ylabel('净收益 (元)')
        ax2.set_title('调度距离 vs 净收益')
        ax2.grid(True, alpha=0.3)

        # 运输成本 vs 预期收益
        transport_costs = [r.transport_cost for r in results]
        revenue_increases = [r.expected_revenue_increase for r in results]
        ax3.scatter(transport_costs, revenue_increases, alpha=0.6, s=50, color='red')
        ax3.plot([0, max(transport_costs)], [0, max(transport_costs)], 'k--', alpha=0.5)
        ax3.set_xlabel('运输成本 (元)')
        ax3.set_ylabel('预期收益增长 (元)')
        ax3.set_title('运输成本 vs 预期收益增长')
        ax3.grid(True, alpha=0.3)

        # 前10个方案的净收益对比
        top_10 = results[:10]
        station_pairs = [f"{r.from_station[:8]}→{r.to_station[:8]}" for r in top_10]
        top_benefits = [r.net_benefit for r in top_10]

        ax4.barh(range(len(top_benefits)), top_benefits, color='lightblue')
        ax4.set_yticks(range(len(station_pairs)))
        ax4.set_yticklabels(station_pairs, fontsize=8)
        ax4.set_xlabel('净收益 (元)')
        ax4.set_title('前10个调度方案净收益对比')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('scheduling_results.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """主程序"""
    print("=== 移动充电车调度优化系统 ===\n")

    # 初始化数据处理器
    processor = DataProcessor()
    processor.load_data()
    processor.clean_and_process_data()

    # 数据质量分析
    quality_report = processor.analyze_data_quality()
    print("\n=== 数据质量报告 ===")
    for category, metrics in quality_report.items():
        print(f"\n{category.upper()}:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value}")

    # 获取场站汇总信息
    station_summary = processor.get_station_summary()
    print(f"\n=== 场站汇总信息 ===")
    print(f"总场站数: {len(station_summary)}")
    print(f"有设备的场站: {(station_summary['实际设备数量'] > 0).sum()}")
    print(f"开放场站: {(station_summary['是否开放。0：否；1：是。'] == 1).sum()}")

    if '平均日收益' in station_summary.columns:
        print(f"有收益数据的场站: {station_summary['平均日收益'].notna().sum()}")
        print(f"平均日收益范围: {station_summary['平均日收益'].min():.2f} - {station_summary['平均日收益'].max():.2f}")

    # 初始化调度优化器
    optimizer = SchedulingOptimizer(processor)

    # 执行调度优化
    print("\n=== 开始调度优化 ===")
    scheduling_results = optimizer.optimize_scheduling(
        max_distance_km=200,  # 最大调度距离200公里
        min_net_benefit=500   # 最小净收益500元
    )

    # 生成报告
    report = optimizer.generate_report(scheduling_results)
    print(report)

    # 保存报告到文件
    with open('scheduling_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    print("调度报告已保存到 scheduling_report.txt")

    # 可视化分析
    visualizer = Visualizer(processor)

    # 分析设备使用率
    utilization_df = optimizer.utilization_analyzer.calculate_utilization_metrics()

    print("\n=== 生成可视化图表 ===")
    try:
        visualizer.plot_station_distribution(utilization_df)
        visualizer.plot_utilization_analysis(utilization_df)
        visualizer.plot_scheduling_results(scheduling_results)
        print("可视化图表已生成并保存")
    except Exception as e:
        print(f"可视化生成失败: {e}")

    # 保存详细数据
    utilization_df.to_csv('station_utilization_analysis.csv', index=False, encoding='utf-8')

    if scheduling_results:
        results_df = pd.DataFrame([
            {
                '源场站': r.from_station,
                '目标场站': r.to_station,
                '调度设备数': r.device_count,
                '距离(km)': r.distance_km,
                '运输成本(元)': r.transport_cost,
                '预期月收益增长(元)': r.expected_revenue_increase,
                '净收益(元)': r.net_benefit
            }
            for r in scheduling_results
        ])
        results_df.to_csv('scheduling_results.csv', index=False, encoding='utf-8')
        print("详细调度结果已保存到 scheduling_results.csv")

    print("\n=== 调度优化完成 ===")

if __name__ == "__main__":
    main()
