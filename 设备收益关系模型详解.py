# "设备-收益"关系模型详细实现

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

class DeviceRevenueModel:
    """设备-收益关系模型"""
    
    def __init__(self):
        # 模型参数 (基于行业经验和历史数据)
        self.model_parameters = {
            'max_revenue_per_device': 500,      # 单设备理论最大日收益(元)
            'optimal_revenue_per_device': 350,  # 单设备理想日收益(元)
            'min_revenue_per_device': 100,      # 单设备最低日收益(元)
            'saturation_factor': 0.8,           # 饱和系数(设备过多时的效率下降)
            'utilization_threshold': 0.7,       # 利用率阈值
        }
    
    def calculate_theoretical_revenue(self, device_count: int, station_type: str = 'standard') -> float:
        """
        计算理论最大收益
        
        Args:
            device_count: 设备数量
            station_type: 场站类型 ('high_traffic', 'standard', 'low_traffic')
        
        Returns:
            理论最大日收益
        """
        # 根据场站类型调整基础收益
        base_revenue = self._get_base_revenue_by_type(station_type)
        
        # 边际效用递减模型
        if device_count <= 3:
            # 前3台设备：线性增长
            theoretical_revenue = device_count * base_revenue
        elif device_count <= 8:
            # 4-8台设备：增长率逐渐下降
            base_3_revenue = 3 * base_revenue
            additional_devices = device_count - 3
            diminishing_factor = 0.9 ** additional_devices  # 每增加1台效率下降10%
            additional_revenue = additional_devices * base_revenue * diminishing_factor
            theoretical_revenue = base_3_revenue + additional_revenue
        else:
            # 超过8台设备：严重边际递减
            base_8_revenue = self.calculate_theoretical_revenue(8, station_type)
            excess_devices = device_count - 8
            # 超额设备的边际收益大幅下降
            excess_revenue = excess_devices * base_revenue * 0.5
            theoretical_revenue = base_8_revenue + excess_revenue
        
        return theoretical_revenue
    
    def _get_base_revenue_by_type(self, station_type: str) -> float:
        """根据场站类型获取基础收益"""
        type_multipliers = {
            'high_traffic': 1.3,    # 高流量场站：商圈、交通枢纽
            'standard': 1.0,        # 标准场站：住宅区、办公区
            'low_traffic': 0.7      # 低流量场站：偏远地区
        }
        
        base_revenue = self.model_parameters['optimal_revenue_per_device']
        return base_revenue * type_multipliers.get(station_type, 1.0)
    
    def calculate_device_efficiency(self, actual_revenue: float, device_count: int, 
                                  station_type: str = 'standard') -> Dict:
        """
        计算设备效率指标
        
        Args:
            actual_revenue: 实际日收益
            device_count: 设备数量
            station_type: 场站类型
        
        Returns:
            设备效率分析结果
        """
        # 理论最大收益
        theoretical_max = self.calculate_theoretical_revenue(device_count, station_type)
        
        # 设备效率计算
        device_efficiency = actual_revenue / theoretical_max if theoretical_max > 0 else 0
        
        # 单设备实际收益
        revenue_per_device = actual_revenue / device_count if device_count > 0 else 0
        
        # 效率等级评定
        efficiency_level = self._classify_efficiency_level(device_efficiency)
        
        # 边际收益分析
        marginal_analysis = self._analyze_marginal_revenue(actual_revenue, device_count, station_type)
        
        return {
            'actual_revenue': actual_revenue,
            'theoretical_max_revenue': theoretical_max,
            'device_count': device_count,
            'device_efficiency': round(device_efficiency, 3),
            'revenue_per_device': round(revenue_per_device, 2),
            'efficiency_level': efficiency_level,
            'marginal_analysis': marginal_analysis,
            'optimization_suggestion': self._generate_optimization_suggestion(
                device_efficiency, marginal_analysis, device_count
            )
        }
    
    def _classify_efficiency_level(self, efficiency: float) -> str:
        """设备效率等级分类"""
        if efficiency >= 0.8:
            return 'excellent'      # 优秀：效率≥80%
        elif efficiency >= 0.6:
            return 'good'          # 良好：60%≤效率<80%
        elif efficiency >= 0.4:
            return 'average'       # 一般：40%≤效率<60%
        elif efficiency >= 0.2:
            return 'poor'          # 较差：20%≤效率<40%
        else:
            return 'very_poor'     # 很差：效率<20%
    
    def _analyze_marginal_revenue(self, actual_revenue: float, device_count: int, 
                                station_type: str) -> Dict:
        """边际收益分析"""
        if device_count <= 1:
            return {'marginal_revenue': actual_revenue, 'recommendation': 'baseline'}
        
        # 计算减少1台设备的理论收益
        revenue_with_less_device = self.calculate_theoretical_revenue(device_count - 1, station_type)
        
        # 计算增加1台设备的理论收益
        revenue_with_more_device = self.calculate_theoretical_revenue(device_count + 1, station_type)
        
        # 当前边际收益 (最后一台设备的贡献)
        current_marginal = actual_revenue - revenue_with_less_device
        
        # 下一台设备的预期边际收益
        next_marginal = revenue_with_more_device - actual_revenue
        
        return {
            'current_marginal_revenue': round(current_marginal, 2),
            'next_marginal_revenue': round(next_marginal, 2),
            'marginal_efficiency': round(current_marginal / (actual_revenue / device_count), 3) if actual_revenue > 0 else 0,
            'recommendation': 'add_device' if next_marginal > 200 else 'remove_device' if current_marginal < 100 else 'maintain'
        }
    
    def estimate_optimal_device_count(self, historical_revenue_data: List[float], 
                                    current_device_count: int, station_type: str = 'standard') -> Dict:
        """
        估算最优设备数量
        
        Args:
            historical_revenue_data: 历史收益数据列表
            current_device_count: 当前设备数量
            station_type: 场站类型
        
        Returns:
            最优设备配置建议
        """
        avg_revenue = np.mean(historical_revenue_data)
        revenue_std = np.std(historical_revenue_data)
        
        # 测试不同设备数量的效率
        device_scenarios = []
        
        for test_device_count in range(1, min(current_device_count + 5, 15)):
            theoretical_revenue = self.calculate_theoretical_revenue(test_device_count, station_type)
            
            # 效率评分 = 实际收益/理论收益 - 设备过多的惩罚
            efficiency_score = avg_revenue / theoretical_revenue if theoretical_revenue > 0 else 0
            
            # 设备过多的惩罚因子
            if test_device_count > 8:
                penalty = (test_device_count - 8) * 0.1
                efficiency_score -= penalty
            
            device_scenarios.append({
                'device_count': test_device_count,
                'theoretical_revenue': theoretical_revenue,
                'efficiency_score': efficiency_score,
                'revenue_per_device': avg_revenue / test_device_count if test_device_count > 0 else 0
            })
        
        # 找到效率最高的配置
        optimal_scenario = max(device_scenarios, key=lambda x: x['efficiency_score'])
        
        return {
            'current_device_count': current_device_count,
            'optimal_device_count': optimal_scenario['device_count'],
            'current_efficiency': avg_revenue / self.calculate_theoretical_revenue(current_device_count, station_type),
            'optimal_efficiency': optimal_scenario['efficiency_score'],
            'improvement_potential': optimal_scenario['efficiency_score'] - (avg_revenue / self.calculate_theoretical_revenue(current_device_count, station_type)),
            'recommendation': self._generate_device_adjustment_recommendation(
                current_device_count, optimal_scenario['device_count']
            ),
            'all_scenarios': device_scenarios
        }
    
    def _generate_optimization_suggestion(self, efficiency: float, marginal_analysis: Dict, 
                                        device_count: int) -> str:
        """生成优化建议"""
        if efficiency < 0.4:
            return f"设备效率过低({efficiency:.1%})，建议减少设备或改善运营"
        elif efficiency > 0.8 and marginal_analysis['next_marginal_revenue'] > 200:
            return f"设备效率优秀({efficiency:.1%})，可考虑增加设备"
        elif marginal_analysis['current_marginal_revenue'] < 100:
            return f"边际收益过低，建议减少{1}台设备"
        else:
            return f"设备配置合理({efficiency:.1%})，维持现状"
    
    def _generate_device_adjustment_recommendation(self, current: int, optimal: int) -> str:
        """生成设备调整建议"""
        diff = optimal - current
        if diff > 0:
            return f"建议增加{diff}台设备，预期提升效率"
        elif diff < 0:
            return f"建议减少{abs(diff)}台设备，优化配置"
        else:
            return "当前设备配置已接近最优"
    
    def visualize_device_revenue_relationship(self, station_type: str = 'standard', 
                                            max_devices: int = 15) -> None:
        """可视化设备-收益关系曲线"""
        device_counts = range(1, max_devices + 1)
        theoretical_revenues = [self.calculate_theoretical_revenue(count, station_type) 
                              for count in device_counts]
        revenue_per_device = [rev / count for rev, count in zip(theoretical_revenues, device_counts)]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 总收益曲线
        ax1.plot(device_counts, theoretical_revenues, 'b-', linewidth=2, label='总收益')
        ax1.set_xlabel('设备数量')
        ax1.set_ylabel('理论日收益(元)')
        ax1.set_title('设备数量 vs 总收益关系')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 单设备收益曲线
        ax2.plot(device_counts, revenue_per_device, 'r-', linewidth=2, label='单设备收益')
        ax2.axhline(y=self.model_parameters['optimal_revenue_per_device'], 
                   color='g', linestyle='--', label='理想单设备收益')
        ax2.set_xlabel('设备数量')
        ax2.set_ylabel('单设备日收益(元)')
        ax2.set_title('设备数量 vs 单设备收益关系')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        plt.tight_layout()
        plt.show()

# 使用示例
if __name__ == "__main__":
    # 创建模型实例
    model = DeviceRevenueModel()
    
    # 示例1：计算设备效率
    efficiency_result = model.calculate_device_efficiency(
        actual_revenue=1200,    # 实际日收益1200元
        device_count=4,         # 4台设备
        station_type='standard' # 标准场站
    )
    
    print("=== 设备效率分析结果 ===")
    print(f"实际收益: {efficiency_result['actual_revenue']}元")
    print(f"理论最大收益: {efficiency_result['theoretical_max_revenue']}元")
    print(f"设备效率: {efficiency_result['device_efficiency']:.1%}")
    print(f"单设备收益: {efficiency_result['revenue_per_device']}元")
    print(f"效率等级: {efficiency_result['efficiency_level']}")
    print(f"优化建议: {efficiency_result['optimization_suggestion']}")
    
    # 示例2：估算最优设备数量
    historical_data = [1000, 1100, 950, 1200, 1050, 1150, 980, 1080]  # 历史收益数据
    optimal_result = model.estimate_optimal_device_count(
        historical_revenue_data=historical_data,
        current_device_count=5,
        station_type='standard'
    )
    
    print("\n=== 最优设备配置分析 ===")
    print(f"当前设备数: {optimal_result['current_device_count']}台")
    print(f"最优设备数: {optimal_result['optimal_device_count']}台")
    print(f"当前效率: {optimal_result['current_efficiency']:.1%}")
    print(f"最优效率: {optimal_result['optimal_efficiency']:.1%}")
    print(f"改进潜力: {optimal_result['improvement_potential']:.1%}")
    print(f"调整建议: {optimal_result['recommendation']}")
    
    # 示例3：可视化设备-收益关系
    print("\n=== 生成设备-收益关系图 ===")
    model.visualize_device_revenue_relationship('standard', 12)
