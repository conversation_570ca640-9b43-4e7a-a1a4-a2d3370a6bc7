# 运输成本计算示例代码

import pandas as pd
import numpy as np
from geopy.distance import geodesic
from typing import Dict, List, Tuple

class TransportCostCalculator:
    """运输成本计算器示例"""
    
    def __init__(self, stations_data: pd.DataFrame):
        self.stations_data = stations_data
        
        # 运输成本参数 (可根据实际情况调整)
        self.cost_parameters = {
            'base_cost': 200,           # 基础运输费用(元) - 包含人工、车辆折旧等
            'cost_per_km': 3,           # 每公里费用(元/km) - 包含燃油、过路费等
            'cost_per_device': 50,      # 每台设备额外费用(元/台) - 装卸、固定等
            'time_cost_per_hour': 100,  # 时间成本(元/小时) - 司机工资、车辆占用等
            'fuel_cost_per_km': 1.5,    # 燃油成本(元/km) - 根据油价调整
            'highway_toll_rate': 0.5,   # 高速费率(元/km) - 长距离运输
        }
    
    def calculate_transport_cost(self, from_station: str, to_station: str, device_count: int = 1) -> Dict:
        """
        计算场站间运输成本
        
        Args:
            from_station: 起始场站名称
            to_station: 目标场站名称  
            device_count: 运输设备数量
            
        Returns:
            包含详细成本分解的字典
        """
        # 获取场站坐标
        from_coords = self._get_station_coords(from_station)
        to_coords = self._get_station_coords(to_station)
        
        if not from_coords or not to_coords:
            return {'error': 'station_not_found', 'message': f'无法找到场站坐标信息'}
        
        # 计算直线距离
        distance_km = self._calculate_distance(from_coords, to_coords)
        
        # 估算实际行驶距离 (考虑道路因素，通常比直线距离长20-30%)
        actual_distance_km = distance_km * 1.25
        
        # 估算运输时间
        transport_time_hours = self._estimate_transport_time(actual_distance_km)
        
        # 详细成本计算
        costs = self._calculate_detailed_costs(actual_distance_km, transport_time_hours, device_count)
        
        total_cost = sum(costs.values())
        
        return {
            'from_station': from_station,
            'to_station': to_station,
            'device_count': device_count,
            'distance_info': {
                'straight_line_km': round(distance_km, 2),
                'actual_distance_km': round(actual_distance_km, 2),
                'transport_time_hours': round(transport_time_hours, 2)
            },
            'cost_breakdown': {k: round(v, 2) for k, v in costs.items()},
            'total_cost': round(total_cost, 2),
            'cost_per_device': round(total_cost / device_count, 2) if device_count > 0 else 0,
            'cost_per_km': round(total_cost / actual_distance_km, 2) if actual_distance_km > 0 else 0
        }
    
    def _get_station_coords(self, station_name: str) -> Tuple[float, float]:
        """获取场站坐标"""
        station = self.stations_data[self.stations_data['名称'] == station_name]
        if len(station) > 0:
            lat = station.iloc[0]['高德_纬度']
            lon = station.iloc[0]['高德_经度']
            return (lat, lon)
        return None
    
    def _calculate_distance(self, coords1: Tuple[float, float], coords2: Tuple[float, float]) -> float:
        """计算两点间直线距离(公里)"""
        return geodesic(coords1, coords2).kilometers
    
    def _estimate_transport_time(self, distance_km: float) -> float:
        """
        估算运输时间(小时)
        考虑不同距离的平均速度
        """
        if distance_km <= 30:
            # 城市内短距离：考虑红绿灯、拥堵等因素
            avg_speed = 35
        elif distance_km <= 100:
            # 城际中距离：部分高速 + 城市道路
            avg_speed = 50
        else:
            # 长距离：主要为高速公路
            avg_speed = 65
        
        # 基础行驶时间
        driving_time = distance_km / avg_speed
        
        # 加上装卸时间 (每次约30分钟)
        loading_time = 1.0  # 1小时 (装车30分钟 + 卸车30分钟)
        
        return driving_time + loading_time
    
    def _calculate_detailed_costs(self, distance_km: float, time_hours: float, device_count: int) -> Dict:
        """计算详细成本分解"""
        costs = {}
        
        # 1. 基础费用 (固定成本)
        costs['base_cost'] = self.cost_parameters['base_cost']
        
        # 2. 距离相关费用
        costs['fuel_cost'] = distance_km * self.cost_parameters['fuel_cost_per_km']
        costs['distance_cost'] = distance_km * self.cost_parameters['cost_per_km']
        
        # 3. 高速费 (距离超过50km时考虑)
        if distance_km > 50:
            highway_distance = distance_km * 0.7  # 假设70%为高速路段
            costs['highway_toll'] = highway_distance * self.cost_parameters['highway_toll_rate']
        else:
            costs['highway_toll'] = 0
        
        # 4. 时间成本
        costs['time_cost'] = time_hours * self.cost_parameters['time_cost_per_hour']
        
        # 5. 设备相关费用
        costs['device_handling_cost'] = device_count * self.cost_parameters['cost_per_device']
        
        # 6. 风险费用 (长距离运输的额外风险成本)
        if distance_km > 200:
            costs['risk_premium'] = distance_km * 0.5
        else:
            costs['risk_premium'] = 0
        
        return costs
    
    def generate_cost_matrix(self) -> pd.DataFrame:
        """生成所有场站间的运输成本矩阵"""
        stations = self.stations_data['名称'].tolist()
        cost_data = []
        
        print(f"正在计算 {len(stations)} 个场站间的运输成本矩阵...")
        
        for i, from_station in enumerate(stations):
            for j, to_station in enumerate(stations):
                if from_station != to_station:
                    cost_info = self.calculate_transport_cost(from_station, to_station, 1)
                    
                    if 'error' not in cost_info:
                        cost_data.append({
                            'from_station': from_station,
                            'to_station': to_station,
                            'distance_km': cost_info['distance_info']['actual_distance_km'],
                            'transport_time_hours': cost_info['distance_info']['transport_time_hours'],
                            'total_cost': cost_info['total_cost'],
                            'cost_per_km': cost_info['cost_per_km']
                        })
            
            if (i + 1) % 10 == 0:
                print(f"已完成 {i + 1}/{len(stations)} 个场站的计算")
        
        return pd.DataFrame(cost_data)
    
    def find_optimal_routes(self, max_cost: float = 1000, max_distance: float = 100) -> pd.DataFrame:
        """
        找到成本和距离都在合理范围内的最优路线
        
        Args:
            max_cost: 最大运输成本限制
            max_distance: 最大运输距离限制(km)
        """
        cost_matrix = self.generate_cost_matrix()
        
        # 筛选符合条件的路线
        optimal_routes = cost_matrix[
            (cost_matrix['total_cost'] <= max_cost) & 
            (cost_matrix['distance_km'] <= max_distance)
        ].copy()
        
        # 按成本效率排序 (成本/距离比)
        optimal_routes['cost_efficiency'] = optimal_routes['total_cost'] / optimal_routes['distance_km']
        optimal_routes = optimal_routes.sort_values('cost_efficiency')
        
        return optimal_routes

# 使用示例
if __name__ == "__main__":
    # 示例场站数据
    stations_data = pd.DataFrame({
        '名称': ['场站A', '场站B', '场站C'],
        '高德_纬度': [31.2304, 31.1304, 31.3304],
        '高德_经度': [121.4737, 121.3737, 121.5737]
    })
    
    # 创建运输成本计算器
    calculator = TransportCostCalculator(stations_data)
    
    # 计算单次运输成本
    cost_result = calculator.calculate_transport_cost('场站A', '场站B', 2)
    print("单次运输成本分析:")
    print(f"总成本: {cost_result['total_cost']}元")
    print(f"距离: {cost_result['distance_info']['actual_distance_km']}公里")
    print(f"时间: {cost_result['distance_info']['transport_time_hours']}小时")
    print("成本分解:", cost_result['cost_breakdown'])
    
    # 生成成本矩阵
    cost_matrix = calculator.generate_cost_matrix()
    print(f"\n成本矩阵生成完成，共 {len(cost_matrix)} 条路线")
    
    # 找到最优路线
    optimal_routes = calculator.find_optimal_routes(max_cost=800, max_distance=80)
    print(f"\n找到 {len(optimal_routes)} 条最优路线")
    print(optimal_routes.head())
