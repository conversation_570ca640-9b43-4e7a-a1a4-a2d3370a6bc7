#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可持续发展的移动充电车调度系统
Sustainable Mobile Charging Vehicle Scheduling System

企业级长期调度应用，支持持续学习、动态优化和实时监控
"""

import pandas as pd
import numpy as np
import sqlite3
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
import threading
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class SchedulingConfig:
    """调度配置"""
    max_distance_km: float = 200
    min_net_benefit: float = 1000
    max_devices_per_move: int = 3
    min_devices_remain: int = 1
    max_total_moves: int = 20
    budget_limit: float = 50000
    learning_rate: float = 0.1
    update_frequency_hours: int = 24
    risk_tolerance: float = 0.3
    
@dataclass
class SchedulingRecord:
    """调度记录"""
    id: str
    timestamp: datetime
    from_station: str
    to_station: str
    device_count: int
    predicted_benefit: float
    actual_benefit: Optional[float]
    transport_cost: float
    distance_km: float
    status: str  # planned, executing, completed, cancelled
    risk_score: float
    confidence: float

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "scheduler.db"):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 调度记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scheduling_records (
                id TEXT PRIMARY KEY,
                timestamp TEXT,
                from_station TEXT,
                to_station TEXT,
                device_count INTEGER,
                predicted_benefit REAL,
                actual_benefit REAL,
                transport_cost REAL,
                distance_km REAL,
                status TEXT,
                risk_score REAL,
                confidence REAL
            )
        ''')
        
        # 场站性能表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS station_performance (
                station_name TEXT,
                date TEXT,
                device_count INTEGER,
                actual_revenue REAL,
                predicted_revenue REAL,
                utilization_rate REAL,
                PRIMARY KEY (station_name, date)
            )
        ''')
        
        # 模型性能表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS model_performance (
                timestamp TEXT PRIMARY KEY,
                prediction_accuracy REAL,
                avg_prediction_error REAL,
                total_benefit_achieved REAL,
                total_cost REAL,
                roi REAL
            )
        ''')
        
        # 系统配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_config (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化完成")
        
    def save_scheduling_record(self, record: SchedulingRecord):
        """保存调度记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO scheduling_records VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            record.id, record.timestamp.isoformat(), record.from_station,
            record.to_station, record.device_count, record.predicted_benefit,
            record.actual_benefit, record.transport_cost, record.distance_km,
            record.status, record.risk_score, record.confidence
        ))
        
        conn.commit()
        conn.close()
        
    def get_historical_performance(self, days: int = 30) -> pd.DataFrame:
        """获取历史性能数据"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT * FROM scheduling_records 
            WHERE timestamp >= datetime('now', '-{} days')
            AND status = 'completed'
        '''.format(days)
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        return df
        
    def save_config(self, config: SchedulingConfig):
        """保存配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        config_dict = asdict(config)
        for key, value in config_dict.items():
            cursor.execute('''
                INSERT OR REPLACE INTO system_config VALUES (?, ?, ?)
            ''', (key, json.dumps(value), datetime.now().isoformat()))
            
        conn.commit()
        conn.close()

class LearningEngine:
    """学习引擎 - 持续优化算法参数"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.prediction_errors = []
        
    def update_model_performance(self):
        """更新模型性能"""
        historical_data = self.db_manager.get_historical_performance(30)
        
        if len(historical_data) == 0:
            return
            
        # 计算预测准确性
        completed_records = historical_data[
            (historical_data['actual_benefit'].notna()) & 
            (historical_data['predicted_benefit'] > 0)
        ]
        
        if len(completed_records) > 0:
            prediction_errors = abs(
                completed_records['actual_benefit'] - completed_records['predicted_benefit']
            ) / completed_records['predicted_benefit']
            
            accuracy = 1 - prediction_errors.mean()
            avg_error = prediction_errors.mean()
            
            # 保存性能指标
            conn = sqlite3.connect(self.db_manager.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO model_performance VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                accuracy,
                avg_error,
                completed_records['actual_benefit'].sum(),
                completed_records['transport_cost'].sum(),
                completed_records['actual_benefit'].sum() / completed_records['transport_cost'].sum()
            ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"模型性能更新: 准确率={accuracy:.3f}, 平均误差={avg_error:.3f}")
            
    def suggest_parameter_adjustments(self, config: SchedulingConfig) -> SchedulingConfig:
        """建议参数调整"""
        historical_data = self.db_manager.get_historical_performance(30)
        
        if len(historical_data) < 10:
            return config
            
        # 分析成功率
        success_rate = len(historical_data[historical_data['actual_benefit'] > 0]) / len(historical_data)
        
        # 根据成功率调整风险容忍度
        if success_rate < 0.7:
            config.risk_tolerance = max(0.1, config.risk_tolerance - 0.05)
            logger.info(f"降低风险容忍度至 {config.risk_tolerance}")
        elif success_rate > 0.9:
            config.risk_tolerance = min(0.5, config.risk_tolerance + 0.05)
            logger.info(f"提高风险容忍度至 {config.risk_tolerance}")
            
        # 根据ROI调整最小净收益要求
        avg_roi = historical_data['actual_benefit'].sum() / historical_data['transport_cost'].sum()
        if avg_roi < 5:
            config.min_net_benefit = config.min_net_benefit * 1.1
            logger.info(f"提高最小净收益要求至 {config.min_net_benefit}")
        elif avg_roi > 20:
            config.min_net_benefit = config.min_net_benefit * 0.9
            logger.info(f"降低最小净收益要求至 {config.min_net_benefit}")
            
        return config

class RealTimeMonitor:
    """实时监控器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.alerts = []
        self.monitoring = False
        
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        monitor_thread = threading.Thread(target=self._monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        logger.info("实时监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        logger.info("实时监控已停止")
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_system_health()
                self._check_performance_anomalies()
                time.sleep(300)  # 每5分钟检查一次
            except Exception as e:
                logger.error(f"监控过程中出错: {e}")
                
    def _check_system_health(self):
        """检查系统健康状态"""
        # 检查最近24小时的调度记录
        recent_data = self.db_manager.get_historical_performance(1)
        
        if len(recent_data) == 0:
            self._add_alert("warning", "过去24小时无调度记录")
            return
            
        # 检查失败率
        failed_count = len(recent_data[recent_data['actual_benefit'] <= 0])
        failure_rate = failed_count / len(recent_data)
        
        if failure_rate > 0.3:
            self._add_alert("critical", f"调度失败率过高: {failure_rate:.2%}")
            
    def _check_performance_anomalies(self):
        """检查性能异常"""
        recent_data = self.db_manager.get_historical_performance(7)
        
        if len(recent_data) < 5:
            return
            
        # 检查预测误差是否异常
        prediction_errors = abs(
            recent_data['actual_benefit'] - recent_data['predicted_benefit']
        ) / recent_data['predicted_benefit']
        
        avg_error = prediction_errors.mean()
        if avg_error > 0.5:
            self._add_alert("warning", f"预测误差过大: {avg_error:.2%}")
            
    def _add_alert(self, level: str, message: str):
        """添加告警"""
        alert = {
            'timestamp': datetime.now(),
            'level': level,
            'message': message
        }
        self.alerts.append(alert)
        logger.warning(f"告警 [{level}]: {message}")
        
        # 保持最近100条告警
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]

class SustainableScheduler:
    """可持续调度器 - 主调度系统"""
    
    def __init__(self, config_path: str = "scheduler_config.json"):
        self.config_path = config_path
        self.db_manager = DatabaseManager()
        self.config = self._load_config()
        self.learning_engine = LearningEngine(self.db_manager)
        self.monitor = RealTimeMonitor(self.db_manager)
        
        # 导入基础调度算法
        try:
            from charging_station_scheduler import DataProcessor, RevenuePredictor, DistanceCalculator

            self.data_processor = DataProcessor()
            self.data_processor.load_data()
            self.data_processor.clean_and_process_data()
        except Exception as e:
            logger.warning(f"数据处理器初始化失败: {e}")
            self.data_processor = None
        
        self.revenue_predictor = RevenuePredictor(self.data_processor.orders_df)
        self.distance_calculator = DistanceCalculator(self.data_processor.stations_df)
        
        logger.info("可持续调度系统初始化完成")
        
    def _load_config(self) -> SchedulingConfig:
        """加载配置"""
        if Path(self.config_path).exists():
            with open(self.config_path, 'r') as f:
                config_dict = json.load(f)
                return SchedulingConfig(**config_dict)
        else:
            config = SchedulingConfig()
            self._save_config(config)
            return config
            
    def _save_config(self, config: SchedulingConfig):
        """保存配置"""
        with open(self.config_path, 'w') as f:
            json.dump(asdict(config), f, indent=2)
        self.db_manager.save_config(config)
        
    def start_system(self):
        """启动系统"""
        logger.info("启动可持续调度系统")
        
        # 启动实时监控
        self.monitor.start_monitoring()
        
        # 启动定期优化任务
        self._start_periodic_optimization()
        
    def _start_periodic_optimization(self):
        """启动定期优化"""
        def optimization_loop():
            while True:
                try:
                    # 更新模型性能
                    self.learning_engine.update_model_performance()
                    
                    # 调整参数
                    new_config = self.learning_engine.suggest_parameter_adjustments(self.config)
                    if new_config != self.config:
                        self.config = new_config
                        self._save_config(self.config)
                        logger.info("配置参数已更新")
                    
                    # 生成新的调度方案
                    self.generate_daily_schedule()
                    
                    # 等待下次更新
                    time.sleep(self.config.update_frequency_hours * 3600)
                    
                except Exception as e:
                    logger.error(f"定期优化过程中出错: {e}")
                    time.sleep(3600)  # 出错后等待1小时再试
                    
        optimization_thread = threading.Thread(target=optimization_loop)
        optimization_thread.daemon = True
        optimization_thread.start()
        
    def generate_daily_schedule(self) -> List[SchedulingRecord]:
        """生成每日调度计划"""
        logger.info("生成每日调度计划")
        
        # 使用改进的调度算法
        from advanced_scheduler import AdvancedSchedulingOptimizer, SchedulingConstraints
        
        optimizer = AdvancedSchedulingOptimizer(self.data_processor)
        
        constraints = SchedulingConstraints(
            max_distance_km=self.config.max_distance_km,
            min_net_benefit=self.config.min_net_benefit,
            max_devices_per_move=self.config.max_devices_per_move,
            min_devices_remain=self.config.min_devices_remain,
            max_total_moves=self.config.max_total_moves,
            budget_limit=self.config.budget_limit
        )
        
        results = optimizer.optimize_with_constraints(constraints)
        
        # 转换为调度记录
        scheduling_records = []
        for result in results:
            record = SchedulingRecord(
                id=f"schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(scheduling_records)}",
                timestamp=datetime.now(),
                from_station=result.from_station,
                to_station=result.to_station,
                device_count=result.device_count,
                predicted_benefit=result.net_benefit,
                actual_benefit=None,
                transport_cost=result.transport_cost,
                distance_km=result.distance_km,
                status="planned",
                risk_score=result.risk_score,
                confidence=0.8  # 默认置信度
            )
            
            # 保存到数据库
            self.db_manager.save_scheduling_record(record)
            scheduling_records.append(record)
            
        logger.info(f"生成了 {len(scheduling_records)} 个调度计划")
        return scheduling_records
        
    def execute_schedule(self, record_id: str) -> bool:
        """执行调度计划"""
        # 这里应该集成实际的调度执行系统
        # 目前只是模拟执行
        logger.info(f"执行调度计划: {record_id}")
        
        # 更新状态为执行中
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE scheduling_records SET status = 'executing' WHERE id = ?
        ''', (record_id,))
        conn.commit()
        conn.close()
        
        return True
        
    def update_actual_result(self, record_id: str, actual_benefit: float):
        """更新实际结果"""
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE scheduling_records 
            SET actual_benefit = ?, status = 'completed' 
            WHERE id = ?
        ''', (actual_benefit, record_id))
        conn.commit()
        conn.close()
        
        logger.info(f"更新调度结果: {record_id}, 实际收益: {actual_benefit}")
        
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        # 获取最近性能数据
        recent_data = self.db_manager.get_historical_performance(7)
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'total_schedules': len(recent_data),
            'success_rate': len(recent_data[recent_data['actual_benefit'] > 0]) / len(recent_data) if len(recent_data) > 0 else 0,
            'avg_roi': recent_data['actual_benefit'].sum() / recent_data['transport_cost'].sum() if len(recent_data) > 0 else 0,
            'alerts': len([a for a in self.monitor.alerts if a['level'] == 'critical']),
            'config': asdict(self.config)
        }
        
        return status
        
    def generate_performance_report(self, days: int = 30) -> str:
        """生成性能报告"""
        historical_data = self.db_manager.get_historical_performance(days)
        
        if len(historical_data) == 0:
            return "暂无历史数据"
            
        report = f"=== 可持续调度系统性能报告 ({days}天) ===\n\n"
        
        # 基础统计
        total_schedules = len(historical_data)
        completed_schedules = len(historical_data[historical_data['status'] == 'completed'])
        success_rate = len(historical_data[historical_data['actual_benefit'] > 0]) / completed_schedules if completed_schedules > 0 else 0
        
        report += f"总调度次数: {total_schedules}\n"
        report += f"完成调度次数: {completed_schedules}\n"
        report += f"成功率: {success_rate:.2%}\n\n"
        
        # 收益分析
        if completed_schedules > 0:
            total_benefit = historical_data['actual_benefit'].sum()
            total_cost = historical_data['transport_cost'].sum()
            roi = total_benefit / total_cost if total_cost > 0 else 0
            
            report += f"总收益: ¥{total_benefit:,.2f}\n"
            report += f"总成本: ¥{total_cost:,.2f}\n"
            report += f"投资回报率: {roi:.2f}\n\n"
            
        # 预测准确性
        completed_with_prediction = historical_data[
            (historical_data['actual_benefit'].notna()) & 
            (historical_data['predicted_benefit'] > 0)
        ]
        
        if len(completed_with_prediction) > 0:
            prediction_errors = abs(
                completed_with_prediction['actual_benefit'] - completed_with_prediction['predicted_benefit']
            ) / completed_with_prediction['predicted_benefit']
            
            accuracy = 1 - prediction_errors.mean()
            report += f"预测准确率: {accuracy:.2%}\n"
            report += f"平均预测误差: {prediction_errors.mean():.2%}\n\n"
            
        # 告警统计
        critical_alerts = len([a for a in self.monitor.alerts if a['level'] == 'critical'])
        warning_alerts = len([a for a in self.monitor.alerts if a['level'] == 'warning'])
        
        report += f"严重告警: {critical_alerts}\n"
        report += f"警告告警: {warning_alerts}\n"
        
        return report

def main():
    """主程序"""
    print("=== 可持续发展移动充电车调度系统 ===\n")
    
    # 初始化系统
    scheduler = SustainableScheduler()
    
    # 启动系统
    scheduler.start_system()
    
    print("系统已启动，正在运行...")
    print("- 实时监控已启用")
    print("- 定期优化已启用")
    print("- 数据库记录已启用")
    
    try:
        while True:
            # 显示系统状态
            status = scheduler.get_system_status()
            print(f"\n当前状态 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}):")
            print(f"  成功率: {status['success_rate']:.2%}")
            print(f"  平均ROI: {status['avg_roi']:.2f}")
            print(f"  严重告警: {status['alerts']}")
            
            # 等待用户输入
            command = input("\n输入命令 (schedule/report/status/quit): ").strip().lower()
            
            if command == 'schedule':
                records = scheduler.generate_daily_schedule()
                print(f"生成了 {len(records)} 个调度计划")
                
            elif command == 'report':
                days = int(input("输入报告天数 (默认30): ") or "30")
                report = scheduler.generate_performance_report(days)
                print(report)
                
            elif command == 'status':
                status = scheduler.get_system_status()
                print(json.dumps(status, indent=2, ensure_ascii=False))
                
            elif command == 'quit':
                break
                
    except KeyboardInterrupt:
        print("\n正在关闭系统...")
        scheduler.monitor.stop_monitoring()
        print("系统已关闭")

if __name__ == "__main__":
    main()
