# 移动充电车调度系统部署指南

## 🚀 快速部署

### 1. 环境要求
- Python 3.8+
- 8GB+ RAM
- 50GB+ 存储空间
- 网络连接

### 2. 安装依赖
```bash
# 克隆项目
git clone <repository_url>
cd mc_dispatch

# 安装Python依赖
pip install -r requirements.txt

# 如果某些包安装失败，可以单独安装
pip install flask flask-cors apscheduler sqlalchemy
```

### 3. 数据准备
确保以下数据文件存在：
- `station_devices.csv` - 场站设备数据
- `final_stations_107.csv` - 场站坐标数据
- `orders/` - 订单数据目录

### 4. 启动系统

#### 方式一：命令行模式
```bash
python3 sustainable_scheduler.py
```

#### 方式二：Web界面模式
```bash
python3 web_interface.py
```
然后访问 http://localhost:5000

## 🏗️ 生产环境部署

### 1. 使用Docker部署

创建 `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "web_interface.py"]
```

构建和运行：
```bash
docker build -t charging-scheduler .
docker run -d -p 5000:5000 -v $(pwd)/data:/app/data charging-scheduler
```

### 2. 使用Nginx反向代理

创建 `nginx.conf`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 使用Systemd服务

创建 `/etc/systemd/system/charging-scheduler.service`:
```ini
[Unit]
Description=Charging Vehicle Scheduler
After=network.target

[Service]
Type=simple
User=scheduler
WorkingDirectory=/opt/charging-scheduler
ExecStart=/usr/bin/python3 web_interface.py
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable charging-scheduler
sudo systemctl start charging-scheduler
```

## 📊 监控和维护

### 1. 日志监控
系统日志位置：
- 应用日志：`scheduler.log`
- 系统日志：`/var/log/syslog`

### 2. 数据库维护
```bash
# 备份数据库
cp scheduler.db scheduler_backup_$(date +%Y%m%d).db

# 清理旧数据（保留90天）
sqlite3 scheduler.db "DELETE FROM scheduling_records WHERE timestamp < datetime('now', '-90 days')"
```

### 3. 性能监控
```bash
# 检查系统资源
htop
df -h
free -h

# 检查应用状态
curl http://localhost:5000/api/status
```

## 🔧 配置管理

### 1. 系统配置文件
`scheduler_config.json`:
```json
{
  "max_distance_km": 200,
  "min_net_benefit": 1000,
  "max_devices_per_move": 3,
  "min_devices_remain": 1,
  "max_total_moves": 20,
  "budget_limit": 50000,
  "learning_rate": 0.1,
  "update_frequency_hours": 24,
  "risk_tolerance": 0.3
}
```

### 2. 环境变量
```bash
export SCHEDULER_DB_PATH="/data/scheduler.db"
export SCHEDULER_LOG_LEVEL="INFO"
export SCHEDULER_WEB_PORT="5000"
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# 只允许必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 2. SSL证书配置
使用Let's Encrypt：
```bash
certbot --nginx -d your-domain.com
```

### 3. 数据库安全
```bash
# 设置数据库文件权限
chmod 600 scheduler.db
chown scheduler:scheduler scheduler.db
```

## 📈 扩展和集成

### 1. API集成
系统提供RESTful API，可以与其他系统集成：

```python
import requests

# 获取系统状态
response = requests.get('http://localhost:5000/api/status')
status = response.json()

# 生成调度计划
response = requests.post('http://localhost:5000/api/schedule')
schedule = response.json()
```

### 2. 数据源集成
可以集成多种数据源：
- ERP系统
- IoT设备数据
- 第三方地图API
- 天气数据API

### 3. 通知系统
集成通知功能：
```python
# 邮件通知
import smtplib
from email.mime.text import MIMEText

def send_alert(message):
    msg = MIMEText(message)
    msg['Subject'] = '调度系统告警'
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>'
    
    smtp = smtplib.SMTP('localhost')
    smtp.send_message(msg)
    smtp.quit()
```

## 🔄 备份和恢复

### 1. 自动备份脚本
创建 `backup.sh`:
```bash
#!/bin/bash
BACKUP_DIR="/backup/scheduler"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
cp scheduler.db $BACKUP_DIR/scheduler_$DATE.db

# 备份配置文件
cp scheduler_config.json $BACKUP_DIR/config_$DATE.json

# 清理30天前的备份
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
```

### 2. 恢复流程
```bash
# 停止服务
sudo systemctl stop charging-scheduler

# 恢复数据库
cp /backup/scheduler/scheduler_20240804_120000.db scheduler.db

# 恢复配置
cp /backup/scheduler/config_20240804_120000.json scheduler_config.json

# 启动服务
sudo systemctl start charging-scheduler
```

## 🐛 故障排除

### 1. 常见问题

**问题：Web界面无法访问**
```bash
# 检查端口是否被占用
netstat -tlnp | grep 5000

# 检查防火墙
ufw status

# 检查应用日志
tail -f scheduler.log
```

**问题：数据库锁定**
```bash
# 检查数据库连接
lsof scheduler.db

# 重启应用
sudo systemctl restart charging-scheduler
```

**问题：内存不足**
```bash
# 检查内存使用
free -h

# 清理缓存
echo 3 > /proc/sys/vm/drop_caches
```

### 2. 性能优化

**数据库优化**
```sql
-- 创建索引
CREATE INDEX idx_timestamp ON scheduling_records(timestamp);
CREATE INDEX idx_station ON scheduling_records(from_station, to_station);

-- 分析查询性能
EXPLAIN QUERY PLAN SELECT * FROM scheduling_records WHERE timestamp > '2024-01-01';
```

**应用优化**
```python
# 使用连接池
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    'sqlite:///scheduler.db',
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20
)
```

## 📞 技术支持

### 1. 日志分析
```bash
# 查看错误日志
grep ERROR scheduler.log

# 查看性能日志
grep "performance" scheduler.log

# 实时监控
tail -f scheduler.log | grep -E "(ERROR|WARNING|CRITICAL)"
```

### 2. 健康检查
```bash
# 创建健康检查脚本
#!/bin/bash
curl -f http://localhost:5000/api/status || exit 1
```

### 3. 联系方式
- 技术支持邮箱：<EMAIL>
- 紧急联系电话：400-xxx-xxxx
- 在线文档：https://docs.company.com

---

**部署完成后，系统将提供：**
- 24/7 自动调度服务
- 实时监控和告警
- Web管理界面
- RESTful API接口
- 数据持久化存储
- 性能分析报告
