#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可持续调度系统测试版本
"""

import pandas as pd
import numpy as np
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import threading
import time
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SchedulingConfig:
    """调度配置"""
    max_distance_km: float = 200
    min_net_benefit: float = 1000
    max_devices_per_move: int = 3
    min_devices_remain: int = 1
    max_total_moves: int = 20
    budget_limit: float = 50000
    learning_rate: float = 0.1
    update_frequency_hours: int = 24
    risk_tolerance: float = 0.3

@dataclass
class SchedulingRecord:
    """调度记录"""
    id: str
    timestamp: datetime
    from_station: str
    to_station: str
    device_count: int
    predicted_benefit: float
    actual_benefit: Optional[float]
    transport_cost: float
    distance_km: float
    status: str
    risk_score: float
    confidence: float

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "test_scheduler.db"):
        self.db_path = db_path
        self.init_database()
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS scheduling_records (
                id TEXT PRIMARY KEY,
                timestamp TEXT,
                from_station TEXT,
                to_station TEXT,
                device_count INTEGER,
                predicted_benefit REAL,
                actual_benefit REAL,
                transport_cost REAL,
                distance_km REAL,
                status TEXT,
                risk_score REAL,
                confidence REAL
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_config (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化完成")
        
    def save_scheduling_record(self, record: SchedulingRecord):
        """保存调度记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO scheduling_records VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            record.id, record.timestamp.isoformat(), record.from_station,
            record.to_station, record.device_count, record.predicted_benefit,
            record.actual_benefit, record.transport_cost, record.distance_km,
            record.status, record.risk_score, record.confidence
        ))
        
        conn.commit()
        conn.close()
        
    def get_historical_performance(self, days: int = 30) -> pd.DataFrame:
        """获取历史性能数据"""
        conn = sqlite3.connect(self.db_path)
        
        query = '''
            SELECT * FROM scheduling_records 
            WHERE timestamp >= datetime('now', '-{} days')
            AND status = 'completed'
        '''.format(days)
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        return df

class TestScheduler:
    """测试调度器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.config = SchedulingConfig()
        logger.info("测试调度系统初始化完成")
        
    def generate_test_schedule(self) -> List[SchedulingRecord]:
        """生成测试调度计划"""
        logger.info("生成测试调度计划")
        
        # 模拟调度记录
        test_records = []
        
        stations = [
            "测试场站A", "测试场站B", "测试场站C", "测试场站D", "测试场站E"
        ]
        
        for i in range(5):
            record = SchedulingRecord(
                id=f"test_schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}",
                timestamp=datetime.now(),
                from_station=stations[i],
                to_station=stations[(i+1) % len(stations)],
                device_count=np.random.randint(1, 4),
                predicted_benefit=np.random.uniform(5000, 50000),
                actual_benefit=None,
                transport_cost=np.random.uniform(200, 2000),
                distance_km=np.random.uniform(10, 200),
                status="planned",
                risk_score=np.random.uniform(0.1, 0.5),
                confidence=np.random.uniform(0.7, 0.9)
            )
            
            self.db_manager.save_scheduling_record(record)
            test_records.append(record)
            
        logger.info(f"生成了 {len(test_records)} 个测试调度计划")
        return test_records
        
    def simulate_execution(self, record_id: str) -> bool:
        """模拟执行调度"""
        logger.info(f"模拟执行调度: {record_id}")
        
        # 模拟执行时间
        time.sleep(1)
        
        # 更新状态
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE scheduling_records SET status = 'executing' WHERE id = ?
        ''', (record_id,))
        conn.commit()
        conn.close()
        
        return True
        
    def update_test_result(self, record_id: str):
        """更新测试结果"""
        # 模拟实际收益
        actual_benefit = np.random.uniform(3000, 45000)
        
        conn = sqlite3.connect(self.db_manager.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE scheduling_records 
            SET actual_benefit = ?, status = 'completed' 
            WHERE id = ?
        ''', (actual_benefit, record_id))
        conn.commit()
        conn.close()
        
        logger.info(f"更新调度结果: {record_id}, 实际收益: {actual_benefit:.2f}")
        
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        recent_data = self.db_manager.get_historical_performance(7)
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'total_schedules': len(recent_data),
            'success_rate': len(recent_data[recent_data['actual_benefit'] > 0]) / len(recent_data) if len(recent_data) > 0 else 0,
            'avg_roi': recent_data['actual_benefit'].sum() / recent_data['transport_cost'].sum() if len(recent_data) > 0 else 0,
            'config': asdict(self.config)
        }
        
        return status
        
    def generate_performance_report(self, days: int = 30) -> str:
        """生成性能报告"""
        historical_data = self.db_manager.get_historical_performance(days)
        
        if len(historical_data) == 0:
            return "暂无历史数据"
            
        report = f"=== 测试调度系统性能报告 ({days}天) ===\n\n"
        
        total_schedules = len(historical_data)
        completed_schedules = len(historical_data[historical_data['status'] == 'completed'])
        success_rate = len(historical_data[historical_data['actual_benefit'] > 0]) / completed_schedules if completed_schedules > 0 else 0
        
        report += f"总调度次数: {total_schedules}\n"
        report += f"完成调度次数: {completed_schedules}\n"
        report += f"成功率: {success_rate:.2%}\n\n"
        
        if completed_schedules > 0:
            total_benefit = historical_data['actual_benefit'].sum()
            total_cost = historical_data['transport_cost'].sum()
            roi = total_benefit / total_cost if total_cost > 0 else 0
            
            report += f"总收益: ¥{total_benefit:,.2f}\n"
            report += f"总成本: ¥{total_cost:,.2f}\n"
            report += f"投资回报率: {roi:.2f}\n\n"
            
        return report

def main():
    """主程序"""
    print("=== 可持续发展移动充电车调度系统 (测试版) ===\n")
    
    # 初始化系统
    scheduler = TestScheduler()
    
    print("系统已启动，正在运行测试...")
    
    try:
        while True:
            # 显示系统状态
            status = scheduler.get_system_status()
            print(f"\n当前状态 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}):")
            print(f"  总调度次数: {status['total_schedules']}")
            print(f"  成功率: {status['success_rate']:.2%}")
            print(f"  平均ROI: {status['avg_roi']:.2f}")
            
            # 等待用户输入
            command = input("\n输入命令 (schedule/execute/report/status/quit): ").strip().lower()
            
            if command == 'schedule':
                records = scheduler.generate_test_schedule()
                print(f"生成了 {len(records)} 个测试调度计划")
                
                # 显示计划详情
                for record in records:
                    print(f"  {record.id}: {record.from_station} → {record.to_station}")
                    print(f"    设备: {record.device_count}台, 预期收益: ¥{record.predicted_benefit:.2f}")
                
            elif command == 'execute':
                # 获取最新的计划记录
                recent_data = scheduler.db_manager.get_historical_performance(1)
                if len(recent_data) > 0:
                    for _, record in recent_data.head(3).iterrows():
                        if record['status'] == 'planned':
                            scheduler.simulate_execution(record['id'])
                            time.sleep(2)  # 模拟执行时间
                            scheduler.update_test_result(record['id'])
                            print(f"执行完成: {record['id']}")
                else:
                    print("没有可执行的调度计划，请先生成计划")
                
            elif command == 'report':
                days = int(input("输入报告天数 (默认7): ") or "7")
                report = scheduler.generate_performance_report(days)
                print(report)
                
            elif command == 'status':
                status = scheduler.get_system_status()
                print(json.dumps(status, indent=2, ensure_ascii=False))
                
            elif command == 'quit':
                break
                
    except KeyboardInterrupt:
        print("\n正在关闭系统...")
        print("系统已关闭")

if __name__ == "__main__":
    main()
