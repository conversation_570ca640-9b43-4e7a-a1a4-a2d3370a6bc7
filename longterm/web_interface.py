#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动充电车调度系统Web界面
Web Interface for Mobile Charging Vehicle Scheduling System
"""

from flask import Flask, render_template, jsonify, request, send_file
from flask_cors import CORS
import json
import pandas as pd
from datetime import datetime, timedelta
import sqlite3
import os
from pathlib import Path

app = Flask(__name__)
CORS(app)

# 全局变量
scheduler = None

def init_scheduler():
    """初始化调度器"""
    global scheduler
    try:
        from sustainable_scheduler import SustainableScheduler
        scheduler = SustainableScheduler()
        return True
    except Exception as e:
        print(f"调度器初始化失败: {e}")
        return False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/status')
def get_status():
    """获取系统状态"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        status = scheduler.get_system_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/schedule', methods=['POST'])
def generate_schedule():
    """生成调度计划"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        records = scheduler.generate_daily_schedule()
        
        # 转换为JSON格式
        schedule_data = []
        for record in records:
            schedule_data.append({
                'id': record.id,
                'from_station': record.from_station,
                'to_station': record.to_station,
                'device_count': record.device_count,
                'predicted_benefit': record.predicted_benefit,
                'transport_cost': record.transport_cost,
                'distance_km': record.distance_km,
                'risk_score': record.risk_score,
                'status': record.status
            })
            
        return jsonify({
            'success': True,
            'count': len(schedule_data),
            'schedules': schedule_data
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/history')
def get_history():
    """获取历史记录"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        days = request.args.get('days', 30, type=int)
        historical_data = scheduler.db_manager.get_historical_performance(days)
        
        if len(historical_data) == 0:
            return jsonify({'data': []})
            
        # 转换为前端需要的格式
        data = historical_data.to_dict('records')
        return jsonify({'data': data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/performance')
def get_performance():
    """获取性能指标"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        days = request.args.get('days', 30, type=int)
        historical_data = scheduler.db_manager.get_historical_performance(days)
        
        if len(historical_data) == 0:
            return jsonify({
                'total_schedules': 0,
                'success_rate': 0,
                'total_benefit': 0,
                'total_cost': 0,
                'roi': 0
            })
            
        completed_data = historical_data[historical_data['status'] == 'completed']
        
        performance = {
            'total_schedules': len(historical_data),
            'completed_schedules': len(completed_data),
            'success_rate': len(completed_data[completed_data['actual_benefit'] > 0]) / len(completed_data) if len(completed_data) > 0 else 0,
            'total_benefit': completed_data['actual_benefit'].sum() if len(completed_data) > 0 else 0,
            'total_cost': completed_data['transport_cost'].sum() if len(completed_data) > 0 else 0,
            'roi': completed_data['actual_benefit'].sum() / completed_data['transport_cost'].sum() if len(completed_data) > 0 and completed_data['transport_cost'].sum() > 0 else 0
        }
        
        return jsonify(performance)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/alerts')
def get_alerts():
    """获取告警信息"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        alerts = []
        for alert in scheduler.monitor.alerts[-20:]:  # 最近20条告警
            alerts.append({
                'timestamp': alert['timestamp'].isoformat(),
                'level': alert['level'],
                'message': alert['message']
            })
            
        return jsonify({'alerts': alerts})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/config', methods=['GET', 'POST'])
def handle_config():
    """处理配置"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    if request.method == 'GET':
        try:
            from dataclasses import asdict
            config_dict = asdict(scheduler.config)
            return jsonify(config_dict)
        except Exception as e:
            return jsonify({'error': str(e)}), 500
            
    elif request.method == 'POST':
        try:
            new_config_data = request.json
            
            # 更新配置
            for key, value in new_config_data.items():
                if hasattr(scheduler.config, key):
                    setattr(scheduler.config, key, value)
                    
            # 保存配置
            scheduler._save_config(scheduler.config)
            
            return jsonify({'success': True, 'message': '配置已更新'})
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@app.route('/api/execute/<schedule_id>', methods=['POST'])
def execute_schedule(schedule_id):
    """执行调度"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        success = scheduler.execute_schedule(schedule_id)
        return jsonify({'success': success})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/update_result/<schedule_id>', methods=['POST'])
def update_result(schedule_id):
    """更新调度结果"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        data = request.json
        actual_benefit = data.get('actual_benefit', 0)
        
        scheduler.update_actual_result(schedule_id, actual_benefit)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/report')
def generate_report():
    """生成报告"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        days = request.args.get('days', 30, type=int)
        report = scheduler.generate_performance_report(days)
        return jsonify({'report': report})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stations')
def get_stations():
    """获取场站信息"""
    try:
        # 读取场站数据
        stations_df = pd.read_csv('final_stations_107.csv')
        stations_data = []
        
        for _, station in stations_df.iterrows():
            stations_data.append({
                'name': station['名称'],
                'device_count': station['设备数量'],
                'latitude': station['高德_纬度'],
                'longitude': station['高德_经度'],
                'is_open': station['是否开放。0：否；1：是。'] == 1,
                'address': station['地址']
            })
            
        return jsonify({'stations': stations_data})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/map_data')
def get_map_data():
    """获取地图数据"""
    if not scheduler:
        return jsonify({'error': '调度器未初始化'}), 500
        
    try:
        # 获取场站数据
        stations_df = pd.read_csv('final_stations_107.csv')
        
        # 获取最近的调度记录
        recent_schedules = scheduler.db_manager.get_historical_performance(7)
        
        map_data = {
            'stations': [],
            'routes': []
        }
        
        # 添加场站数据
        for _, station in stations_df.iterrows():
            map_data['stations'].append({
                'name': station['名称'],
                'lat': station['高德_纬度'],
                'lon': station['高德_经度'],
                'device_count': station['设备数量'],
                'is_open': station['是否开放。0：否；1：是。'] == 1
            })
            
        # 添加调度路线
        for _, schedule in recent_schedules.head(10).iterrows():
            from_station = stations_df[stations_df['名称'] == schedule['from_station']]
            to_station = stations_df[stations_df['名称'] == schedule['to_station']]
            
            if not from_station.empty and not to_station.empty:
                map_data['routes'].append({
                    'from': {
                        'name': schedule['from_station'],
                        'lat': from_station.iloc[0]['高德_纬度'],
                        'lon': from_station.iloc[0]['高德_经度']
                    },
                    'to': {
                        'name': schedule['to_station'],
                        'lat': to_station.iloc[0]['高德_纬度'],
                        'lon': to_station.iloc[0]['高德_经度']
                    },
                    'benefit': schedule.get('actual_benefit', schedule.get('predicted_benefit', 0)),
                    'status': schedule['status']
                })
                
        return jsonify(map_data)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 创建模板目录和基础HTML文件
def create_templates():
    """创建模板文件"""
    templates_dir = Path('templates')
    templates_dir.mkdir(exist_ok=True)
    
    # 创建基础HTML模板
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动充电车调度系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #2980b9; }
        .btn-success { background: #27ae60; }
        .btn-warning { background: #f39c12; }
        .btn-danger { background: #e74c3c; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #27ae60; }
        .status-warning { background: #f39c12; }
        .status-error { background: #e74c3c; }
        #map { height: 400px; width: 100%; }
        .schedule-item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .metric { text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #2c3e50; }
        .metric-label { color: #7f8c8d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>移动充电车调度系统</h1>
            <p>智能调度 · 收益最大化 · 可持续发展</p>
        </div>
        
        <div class="dashboard">
            <!-- 系统状态 -->
            <div class="card">
                <h3>系统状态</h3>
                <div id="system-status">
                    <div class="metric">
                        <div class="metric-value" id="success-rate">--</div>
                        <div class="metric-label">成功率</div>
                    </div>
                </div>
                <button class="btn" onclick="refreshStatus()">刷新状态</button>
            </div>
            
            <!-- 快速操作 -->
            <div class="card">
                <h3>快速操作</h3>
                <button class="btn btn-success" onclick="generateSchedule()">生成调度计划</button>
                <button class="btn btn-warning" onclick="showReport()">查看报告</button>
                <button class="btn" onclick="showConfig()">系统配置</button>
            </div>
            
            <!-- 性能指标 -->
            <div class="card">
                <h3>性能指标</h3>
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
            
            <!-- 告警信息 -->
            <div class="card">
                <h3>告警信息</h3>
                <div id="alerts-list">
                    <p>暂无告警</p>
                </div>
            </div>
        </div>
        
        <!-- 地图 -->
        <div class="card" style="margin-top: 20px;">
            <h3>调度地图</h3>
            <div id="map"></div>
        </div>
        
        <!-- 调度计划 -->
        <div class="card" style="margin-top: 20px;">
            <h3>最新调度计划</h3>
            <div id="schedule-list">
                <p>暂无调度计划</p>
            </div>
        </div>
    </div>
    
    <script>
        let map;
        let performanceChart;
        
        // 初始化地图
        function initMap() {
            map = L.map('map').setView([31.8, 117.2], 8);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
        }
        
        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'ROI',
                        data: [],
                        borderColor: '#3498db',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }
        
        // 刷新系统状态
        function refreshStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.error);
                        return;
                    }
                    document.getElementById('success-rate').textContent = 
                        (data.success_rate * 100).toFixed(1) + '%';
                })
                .catch(error => console.error('Error:', error));
        }
        
        // 生成调度计划
        function generateSchedule() {
            fetch('/api/schedule', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`成功生成 ${data.count} 个调度计划`);
                        loadSchedules();
                    } else {
                        alert('生成失败: ' + data.error);
                    }
                })
                .catch(error => console.error('Error:', error));
        }
        
        // 加载调度计划
        function loadSchedules() {
            fetch('/api/history?days=1')
                .then(response => response.json())
                .then(data => {
                    const scheduleList = document.getElementById('schedule-list');
                    if (data.data && data.data.length > 0) {
                        scheduleList.innerHTML = data.data.map(schedule => `
                            <div class="schedule-item">
                                <strong>${schedule.from_station}</strong> → <strong>${schedule.to_station}</strong><br>
                                设备: ${schedule.device_count}台 | 
                                预期收益: ¥${schedule.predicted_benefit?.toFixed(2) || 0} | 
                                状态: ${schedule.status}
                            </div>
                        `).join('');
                    } else {
                        scheduleList.innerHTML = '<p>暂无调度计划</p>';
                    }
                })
                .catch(error => console.error('Error:', error));
        }
        
        // 显示报告
        function showReport() {
            fetch('/api/report?days=30')
                .then(response => response.json())
                .then(data => {
                    if (data.report) {
                        alert(data.report);
                    }
                })
                .catch(error => console.error('Error:', error));
        }
        
        // 显示配置
        function showConfig() {
            fetch('/api/config')
                .then(response => response.json())
                .then(data => {
                    const configStr = JSON.stringify(data, null, 2);
                    alert('当前配置:\\n' + configStr);
                })
                .catch(error => console.error('Error:', error));
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initMap();
            initChart();
            refreshStatus();
            loadSchedules();
            
            // 定期刷新状态
            setInterval(refreshStatus, 30000); // 每30秒刷新一次
        });
    </script>
</body>
</html>'''
    
    with open(templates_dir / 'index.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == '__main__':
    print("正在启动Web界面...")
    
    # 创建模板文件
    create_templates()
    
    # 初始化调度器
    if init_scheduler():
        print("调度器初始化成功")
        scheduler.start_system()
    else:
        print("调度器初始化失败，部分功能可能不可用")
    
    print("Web界面启动成功!")
    print("请访问: http://localhost:5000")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
