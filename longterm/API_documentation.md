# 移动充电车调度系统 API 文档

## 📋 概述

本文档描述了移动充电车调度系统的RESTful API接口，支持调度计划生成、系统监控、配置管理等功能。

**基础URL**: `http://localhost:5000/api`

**认证方式**: 暂无（内部系统）

**数据格式**: JSON

## 🔗 API 端点

### 1. 系统状态

#### GET /status
获取系统当前状态和关键指标。

**请求示例**:
```bash
curl -X GET http://localhost:5000/api/status
```

**响应示例**:
```json
{
  "timestamp": "2024-08-04T12:00:00",
  "total_schedules": 45,
  "success_rate": 0.89,
  "avg_roi": 25.6,
  "alerts": 2,
  "config": {
    "max_distance_km": 200,
    "min_net_benefit": 1000,
    "budget_limit": 50000
  }
}
```

**响应字段**:
- `timestamp`: 状态更新时间
- `total_schedules`: 总调度次数
- `success_rate`: 成功率 (0-1)
- `avg_roi`: 平均投资回报率
- `alerts`: 严重告警数量
- `config`: 当前系统配置

### 2. 调度管理

#### POST /schedule
生成新的调度计划。

**请求示例**:
```bash
curl -X POST http://localhost:5000/api/schedule \
  -H "Content-Type: application/json"
```

**响应示例**:
```json
{
  "success": true,
  "count": 15,
  "schedules": [
    {
      "id": "schedule_20240804_120000_0",
      "from_station": "易佳电蓝鲤皇藏峪路充电站",
      "to_station": "易佳电蜂鸟北城美食城充电站",
      "device_count": 1,
      "predicted_benefit": 37707.16,
      "transport_cost": 292.19,
      "distance_km": 14.06,
      "risk_score": 0.183,
      "status": "planned"
    }
  ]
}
```

#### POST /execute/{schedule_id}
执行指定的调度计划。

**请求示例**:
```bash
curl -X POST http://localhost:5000/api/execute/schedule_20240804_120000_0
```

**响应示例**:
```json
{
  "success": true
}
```

#### POST /update_result/{schedule_id}
更新调度执行结果。

**请求示例**:
```bash
curl -X POST http://localhost:5000/api/update_result/schedule_20240804_120000_0 \
  -H "Content-Type: application/json" \
  -d '{"actual_benefit": 35000.50}'
```

**请求参数**:
- `actual_benefit`: 实际收益金额

**响应示例**:
```json
{
  "success": true
}
```

### 3. 历史数据

#### GET /history
获取历史调度记录。

**请求参数**:
- `days` (可选): 查询天数，默认30天

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/history?days=7"
```

**响应示例**:
```json
{
  "data": [
    {
      "id": "schedule_20240804_120000_0",
      "timestamp": "2024-08-04T12:00:00",
      "from_station": "易佳电蓝鲤皇藏峪路充电站",
      "to_station": "易佳电蜂鸟北城美食城充电站",
      "device_count": 1,
      "predicted_benefit": 37707.16,
      "actual_benefit": 35000.50,
      "transport_cost": 292.19,
      "distance_km": 14.06,
      "status": "completed",
      "risk_score": 0.183,
      "confidence": 0.8
    }
  ]
}
```

### 4. 性能指标

#### GET /performance
获取系统性能指标。

**请求参数**:
- `days` (可选): 统计天数，默认30天

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/performance?days=30"
```

**响应示例**:
```json
{
  "total_schedules": 150,
  "completed_schedules": 142,
  "success_rate": 0.89,
  "total_benefit": 2418960.50,
  "total_cost": 51811.20,
  "roi": 46.69
}
```

### 5. 告警管理

#### GET /alerts
获取系统告警信息。

**请求示例**:
```bash
curl -X GET http://localhost:5000/api/alerts
```

**响应示例**:
```json
{
  "alerts": [
    {
      "timestamp": "2024-08-04T11:30:00",
      "level": "warning",
      "message": "预测误差过大: 25%"
    },
    {
      "timestamp": "2024-08-04T10:15:00",
      "level": "critical",
      "message": "调度失败率过高: 35%"
    }
  ]
}
```

**告警级别**:
- `critical`: 严重告警，需要立即处理
- `warning`: 警告，需要关注
- `info`: 信息提示

### 6. 配置管理

#### GET /config
获取系统配置。

**请求示例**:
```bash
curl -X GET http://localhost:5000/api/config
```

**响应示例**:
```json
{
  "max_distance_km": 200,
  "min_net_benefit": 1000,
  "max_devices_per_move": 3,
  "min_devices_remain": 1,
  "max_total_moves": 20,
  "budget_limit": 50000,
  "learning_rate": 0.1,
  "update_frequency_hours": 24,
  "risk_tolerance": 0.3
}
```

#### POST /config
更新系统配置。

**请求示例**:
```bash
curl -X POST http://localhost:5000/api/config \
  -H "Content-Type: application/json" \
  -d '{
    "max_distance_km": 250,
    "min_net_benefit": 1200,
    "budget_limit": 60000
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "配置已更新"
}
```

### 7. 报告生成

#### GET /report
生成性能报告。

**请求参数**:
- `days` (可选): 报告天数，默认30天

**请求示例**:
```bash
curl -X GET "http://localhost:5000/api/report?days=30"
```

**响应示例**:
```json
{
  "report": "=== 可持续调度系统性能报告 (30天) ===\n\n总调度次数: 150\n完成调度次数: 142\n成功率: 89.00%\n\n总收益: ¥2,418,960.50\n总成本: ¥51,811.20\n投资回报率: 46.69\n\n预测准确率: 85.20%\n平均预测误差: 12.30%\n\n严重告警: 2\n警告告警: 8"
}
```

### 8. 地图数据

#### GET /stations
获取所有场站信息。

**请求示例**:
```bash
curl -X GET http://localhost:5000/api/stations
```

**响应示例**:
```json
{
  "stations": [
    {
      "name": "上海国轩高科总部大楼充电站",
      "device_count": 25,
      "latitude": 31.347283,
      "longitude": 121.183391,
      "is_open": false,
      "address": "上海,上海市,嘉定区"
    }
  ]
}
```

#### GET /map_data
获取地图可视化数据。

**请求示例**:
```bash
curl -X GET http://localhost:5000/api/map_data
```

**响应示例**:
```json
{
  "stations": [
    {
      "name": "上海国轩高科总部大楼充电站",
      "lat": 31.347283,
      "lon": 121.183391,
      "device_count": 25,
      "is_open": false
    }
  ],
  "routes": [
    {
      "from": {
        "name": "易佳电蓝鲤皇藏峪路充电站",
        "lat": 31.910316,
        "lon": 117.342058
      },
      "to": {
        "name": "易佳电蜂鸟北城美食城充电站",
        "lat": 32.01128,
        "lon": 117.252038
      },
      "benefit": 35000.50,
      "status": "completed"
    }
  ]
}
```

## 🔧 错误处理

### 错误响应格式
```json
{
  "error": "错误描述信息"
}
```

### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 常见错误
- `调度器未初始化`: 系统启动失败
- `数据库连接失败`: 数据库不可用
- `配置参数无效`: 提交的配置参数不正确

## 📊 使用示例

### Python客户端示例
```python
import requests
import json

class SchedulerClient:
    def __init__(self, base_url="http://localhost:5000/api"):
        self.base_url = base_url
    
    def get_status(self):
        response = requests.get(f"{self.base_url}/status")
        return response.json()
    
    def generate_schedule(self):
        response = requests.post(f"{self.base_url}/schedule")
        return response.json()
    
    def update_config(self, config):
        response = requests.post(
            f"{self.base_url}/config",
            json=config
        )
        return response.json()

# 使用示例
client = SchedulerClient()
status = client.get_status()
print(f"系统成功率: {status['success_rate']:.2%}")

schedule = client.generate_schedule()
print(f"生成了 {schedule['count']} 个调度计划")
```

### JavaScript客户端示例
```javascript
class SchedulerAPI {
    constructor(baseURL = 'http://localhost:5000/api') {
        this.baseURL = baseURL;
    }
    
    async getStatus() {
        const response = await fetch(`${this.baseURL}/status`);
        return response.json();
    }
    
    async generateSchedule() {
        const response = await fetch(`${this.baseURL}/schedule`, {
            method: 'POST'
        });
        return response.json();
    }
    
    async getHistory(days = 30) {
        const response = await fetch(`${this.baseURL}/history?days=${days}`);
        return response.json();
    }
}

// 使用示例
const api = new SchedulerAPI();

api.getStatus().then(status => {
    console.log(`系统成功率: ${(status.success_rate * 100).toFixed(1)}%`);
});

api.generateSchedule().then(result => {
    console.log(`生成了 ${result.count} 个调度计划`);
});
```

## 🔄 Webhook支持

系统支持Webhook回调，可以在特定事件发生时通知外部系统。

### 配置Webhook
```json
{
  "webhook_url": "https://your-system.com/webhook",
  "events": ["schedule_completed", "alert_critical"]
}
```

### Webhook事件
- `schedule_completed`: 调度完成
- `schedule_failed`: 调度失败
- `alert_critical`: 严重告警
- `system_startup`: 系统启动
- `config_updated`: 配置更新

### Webhook负载示例
```json
{
  "event": "schedule_completed",
  "timestamp": "2024-08-04T12:00:00",
  "data": {
    "schedule_id": "schedule_20240804_120000_0",
    "actual_benefit": 35000.50,
    "predicted_benefit": 37707.16,
    "accuracy": 0.93
  }
}
```

---

**API版本**: v1.0  
**最后更新**: 2024-08-04  
**技术支持**: <EMAIL>
