#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级移动充电车调度优化系统
Advanced Mobile Charging Vehicle Scheduling Optimization System

包含更复杂的优化策略和评估指标
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass, field
from geopy.distance import geodesic
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from scipy.optimize import minimize
import itertools

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

@dataclass
class AdvancedSchedulingResult:
    """高级调度结果数据类"""
    from_station: str
    to_station: str
    device_count: int
    expected_revenue_increase: float
    transport_cost: float
    net_benefit: float
    distance_km: float
    roi: float  # 投资回报率
    payback_days: float  # 回本天数
    risk_score: float  # 风险评分
    priority_score: float  # 优先级评分
    
@dataclass
class SchedulingConstraints:
    """调度约束条件"""
    max_distance_km: float = 200
    min_net_benefit: float = 500
    max_devices_per_move: int = 5
    min_devices_remain: int = 1
    max_total_moves: int = 20
    budget_limit: float = 50000
    
class AdvancedSchedulingOptimizer:
    """高级调度优化器"""
    
    def __init__(self, data_processor):
        self.data_processor = data_processor
        self.stations_df = data_processor.stations_df
        self.orders_df = data_processor.orders_df
        self.distance_matrix = {}
        self.station_clusters = {}
        
    def calculate_distance(self, station1: str, station2: str) -> float:
        """计算两个场站之间的距离（公里）"""
        if station1 == station2:
            return 0.0
            
        key = tuple(sorted([station1, station2]))
        if key in self.distance_matrix:
            return self.distance_matrix[key]
            
        s1_data = self.stations_df[self.stations_df['名称'] == station1]
        s2_data = self.stations_df[self.stations_df['名称'] == station2]
        
        if s1_data.empty or s2_data.empty:
            return float('inf')
            
        coord1 = (s1_data.iloc[0]['高德_纬度'], s1_data.iloc[0]['高德_经度'])
        coord2 = (s2_data.iloc[0]['高德_纬度'], s2_data.iloc[0]['高德_经度'])
        
        distance = geodesic(coord1, coord2).kilometers
        self.distance_matrix[key] = distance
        
        return distance
        
    def calculate_advanced_transport_cost(self, distance_km: float, device_count: int, 
                                        time_of_day: str = 'normal') -> float:
        """计算高级运输成本（考虑时间段、设备类型等）"""
        base_cost = 200
        distance_cost = distance_km * 3
        device_cost = device_count * 50
        
        # 时间段调整
        time_multiplier = {
            'peak': 1.5,      # 高峰期
            'normal': 1.0,    # 正常时段
            'night': 0.8      # 夜间
        }.get(time_of_day, 1.0)
        
        # 距离折扣（长距离有一定折扣）
        distance_discount = 1.0 if distance_km < 100 else 0.95 if distance_km < 200 else 0.9
        
        total_cost = (base_cost + distance_cost + device_cost) * time_multiplier * distance_discount
        
        return total_cost
        
    def predict_revenue_with_seasonality(self, station_name: str, device_count: int, 
                                       days_ahead: int = 30) -> Dict:
        """预测收益（考虑季节性和趋势）"""
        station_data = self.orders_df[self.orders_df['station_name'] == station_name].copy()
        
        if len(station_data) < 10:
            return {
                'daily_avg': 0,
                'monthly_total': 0,
                'confidence': 'low',
                'volatility': 1.0
            }
            
        # 计算基础统计
        daily_avg = station_data['daily_revenue'].mean()
        daily_std = station_data['daily_revenue'].std()
        
        # 季节性调整
        current_month = datetime.now().month
        seasonal_multiplier = {
            1: 0.9, 2: 0.85, 3: 1.0, 4: 1.1, 5: 1.15, 6: 1.2,
            7: 1.25, 8: 1.2, 9: 1.1, 10: 1.05, 11: 0.95, 12: 0.9
        }.get(current_month, 1.0)
        
        # 趋势分析
        station_data['date'] = pd.to_datetime(station_data['date'])
        station_data = station_data.sort_values('date')
        
        if len(station_data) > 30:
            recent_avg = station_data.tail(15)['daily_revenue'].mean()
            earlier_avg = station_data.head(15)['daily_revenue'].mean()
            trend_multiplier = recent_avg / earlier_avg if earlier_avg > 0 else 1.0
        else:
            trend_multiplier = 1.0
            
        # 设备数量影响（边际效应递减）
        device_efficiency = 1.0 if device_count <= 1 else (
            0.9 if device_count <= 3 else 0.8 if device_count <= 5 else 0.7
        )
        
        adjusted_daily_avg = daily_avg * seasonal_multiplier * trend_multiplier * device_efficiency
        monthly_total = adjusted_daily_avg * days_ahead
        
        return {
            'daily_avg': adjusted_daily_avg,
            'monthly_total': monthly_total,
            'confidence': 'high' if len(station_data) > 50 else 'medium',
            'volatility': daily_std / daily_avg if daily_avg > 0 else 1.0
        }
        
    def calculate_risk_score(self, from_station: str, to_station: str, 
                           device_count: int, distance_km: float) -> float:
        """计算风险评分（0-1，越低越好）"""
        risk_factors = []
        
        # 距离风险
        distance_risk = min(distance_km / 300, 1.0)
        risk_factors.append(distance_risk * 0.3)
        
        # 收益波动风险
        from_prediction = self.predict_revenue_with_seasonality(from_station, device_count)
        to_prediction = self.predict_revenue_with_seasonality(to_station, device_count)
        
        volatility_risk = (from_prediction['volatility'] + to_prediction['volatility']) / 2
        risk_factors.append(min(volatility_risk, 1.0) * 0.4)
        
        # 设备数量风险（调度太多设备风险更高）
        device_risk = device_count / 10  # 假设最多10台设备
        risk_factors.append(min(device_risk, 1.0) * 0.2)
        
        # 数据可信度风险
        confidence_risk = {
            'high': 0.1,
            'medium': 0.3,
            'low': 0.6
        }.get(to_prediction['confidence'], 0.5)
        risk_factors.append(confidence_risk * 0.1)
        
        return sum(risk_factors)
        
    def cluster_stations(self, n_clusters: int = 5) -> Dict[str, int]:
        """对场站进行聚类分析"""
        # 准备聚类特征
        features = []
        station_names = []
        
        for _, station in self.stations_df.iterrows():
            station_name = station['名称']
            
            # 获取收益数据
            station_orders = self.orders_df[self.orders_df['station_name'] == station_name]
            
            if len(station_orders) > 0:
                avg_revenue = station_orders['daily_revenue'].mean()
                revenue_std = station_orders['daily_revenue'].std()
                device_count = station['实际设备数量']
                
                features.append([
                    station['高德_经度'],
                    station['高德_纬度'],
                    avg_revenue,
                    device_count,
                    revenue_std if not pd.isna(revenue_std) else 0
                ])
                station_names.append(station_name)
        
        if len(features) < n_clusters:
            return {}
            
        # 标准化特征
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(features_scaled)
        
        # 返回站点到聚类的映射
        return dict(zip(station_names, cluster_labels))
        
    def optimize_with_constraints(self, constraints: SchedulingConstraints) -> List[AdvancedSchedulingResult]:
        """带约束条件的优化调度"""
        print("正在执行高级调度优化...")
        
        # 获取所有可能的调度组合
        all_stations = self.stations_df['名称'].tolist()
        potential_moves = []
        
        # 计算每个场站的设备利用率
        station_utilization = {}
        for station in all_stations:
            station_orders = self.orders_df[self.orders_df['station_name'] == station]
            station_info = self.stations_df[self.stations_df['名称'] == station].iloc[0]
            
            if len(station_orders) > 0 and station_info['实际设备数量'] > 0:
                avg_revenue = station_orders['daily_revenue'].mean()
                device_count = station_info['实际设备数量']
                utilization = avg_revenue / device_count
                station_utilization[station] = {
                    'utilization': utilization,
                    'device_count': device_count,
                    'avg_revenue': avg_revenue
                }
        
        # 识别低利用率和高利用率场站
        sorted_stations = sorted(station_utilization.items(), 
                               key=lambda x: x[1]['utilization'])
        
        low_util_stations = [s[0] for s in sorted_stations[:len(sorted_stations)//3]]
        high_util_stations = [s[0] for s in sorted_stations[-len(sorted_stations)//3:]]
        
        # 生成调度方案
        for from_station in low_util_stations:
            for to_station in high_util_stations:
                if from_station == to_station:
                    continue
                    
                from_info = station_utilization[from_station]
                to_info = station_utilization[to_station]
                
                # 确定可调度的设备数量
                max_movable = min(
                    from_info['device_count'] - constraints.min_devices_remain,
                    constraints.max_devices_per_move
                )
                
                if max_movable <= 0:
                    continue
                    
                for device_count in range(1, max_movable + 1):
                    distance = self.calculate_distance(from_station, to_station)
                    
                    if distance > constraints.max_distance_km:
                        continue
                        
                    # 计算成本和收益
                    transport_cost = self.calculate_advanced_transport_cost(distance, device_count)
                    
                    if transport_cost > constraints.budget_limit / constraints.max_total_moves:
                        continue
                        
                    # 预测收益变化
                    from_prediction = self.predict_revenue_with_seasonality(from_station, device_count)
                    to_prediction = self.predict_revenue_with_seasonality(to_station, device_count)
                    
                    revenue_loss = from_prediction['monthly_total'] * 0.1  # 源站损失
                    revenue_gain = to_prediction['monthly_total'] * 0.3   # 目标站增益
                    net_revenue_change = revenue_gain - revenue_loss
                    
                    net_benefit = net_revenue_change - transport_cost
                    
                    if net_benefit < constraints.min_net_benefit:
                        continue
                        
                    # 计算高级指标
                    roi = net_benefit / transport_cost if transport_cost > 0 else 0
                    payback_days = transport_cost / (net_revenue_change / 30) if net_revenue_change > 0 else float('inf')
                    risk_score = self.calculate_risk_score(from_station, to_station, device_count, distance)
                    
                    # 优先级评分（综合考虑收益、风险、ROI）
                    priority_score = (roi * 0.4 + (1 - risk_score) * 0.3 + 
                                    min(net_benefit / 10000, 1) * 0.3)
                    
                    result = AdvancedSchedulingResult(
                        from_station=from_station,
                        to_station=to_station,
                        device_count=device_count,
                        expected_revenue_increase=net_revenue_change,
                        transport_cost=transport_cost,
                        net_benefit=net_benefit,
                        distance_km=distance,
                        roi=roi,
                        payback_days=payback_days,
                        risk_score=risk_score,
                        priority_score=priority_score
                    )
                    
                    potential_moves.append(result)
        
        # 按优先级评分排序
        potential_moves.sort(key=lambda x: x.priority_score, reverse=True)
        
        # 选择最优方案（避免冲突）
        selected_moves = []
        used_from_stations = set()
        used_to_stations = set()
        total_cost = 0
        
        for move in potential_moves:
            if (move.from_station not in used_from_stations and 
                move.to_station not in used_to_stations and
                total_cost + move.transport_cost <= constraints.budget_limit and
                len(selected_moves) < constraints.max_total_moves):
                
                selected_moves.append(move)
                used_from_stations.add(move.from_station)
                used_to_stations.add(move.to_station)
                total_cost += move.transport_cost
        
        return selected_moves
        
    def generate_advanced_report(self, results: List[AdvancedSchedulingResult]) -> str:
        """生成高级调度报告"""
        if not results:
            return "未找到满足约束条件的调度方案。"
            
        report = "=== 高级移动充电车调度优化报告 ===\n\n"
        
        # 总体统计
        total_moves = len(results)
        total_devices = sum(r.device_count for r in results)
        total_cost = sum(r.transport_cost for r in results)
        total_benefit = sum(r.net_benefit for r in results)
        avg_roi = np.mean([r.roi for r in results])
        avg_risk = np.mean([r.risk_score for r in results])
        avg_payback = np.mean([r.payback_days for r in results if r.payback_days != float('inf')])
        
        report += f"调度方案总数: {total_moves}\n"
        report += f"总调度设备数: {total_devices} 台\n"
        report += f"总运输成本: ¥{total_cost:,.2f}\n"
        report += f"总预期净收益: ¥{total_benefit:,.2f}\n"
        report += f"平均投资回报率: {avg_roi:.2f}\n"
        report += f"平均风险评分: {avg_risk:.3f}\n"
        report += f"平均回本天数: {avg_payback:.1f} 天\n\n"
        
        # 详细方案
        report += "详细调度方案 (按优先级排序):\n"
        report += "=" * 100 + "\n"
        
        for i, result in enumerate(results, 1):
            report += f"{i}. {result.from_station} → {result.to_station}\n"
            report += f"   设备数量: {result.device_count} 台\n"
            report += f"   距离: {result.distance_km:.1f} km\n"
            report += f"   运输成本: ¥{result.transport_cost:,.2f}\n"
            report += f"   预期月收益增长: ¥{result.expected_revenue_increase:,.2f}\n"
            report += f"   净收益: ¥{result.net_benefit:,.2f}\n"
            report += f"   投资回报率: {result.roi:.2f}\n"
            report += f"   回本天数: {result.payback_days:.1f} 天\n"
            report += f"   风险评分: {result.risk_score:.3f}\n"
            report += f"   优先级评分: {result.priority_score:.3f}\n"
            report += "-" * 50 + "\n"
            
        return report

class AdvancedVisualizer:
    """高级可视化器"""

    def __init__(self, optimizer: AdvancedSchedulingOptimizer):
        self.optimizer = optimizer

    def create_interactive_map(self, results: List[AdvancedSchedulingResult]) -> None:
        """创建交互式调度地图"""
        if not results:
            print("没有调度结果可以可视化")
            return

        stations_df = self.optimizer.stations_df

        # 创建地图数据
        map_data = []

        # 添加所有场站
        for _, station in stations_df.iterrows():
            map_data.append({
                'name': station['名称'],
                'lat': station['高德_纬度'],
                'lon': station['高德_经度'],
                'type': 'station',
                'devices': station['实际设备数量'],
                'size': max(station['实际设备数量'] * 2, 5)
            })

        # 添加调度路线
        for i, result in enumerate(results[:10]):  # 只显示前10个
            from_station = stations_df[stations_df['名称'] == result.from_station].iloc[0]
            to_station = stations_df[stations_df['名称'] == result.to_station].iloc[0]

            map_data.append({
                'name': f"调度{i+1}: {result.device_count}台设备",
                'lat': [from_station['高德_纬度'], to_station['高德_纬度']],
                'lon': [from_station['高德_经度'], to_station['高德_经度']],
                'type': 'route',
                'benefit': result.net_benefit,
                'distance': result.distance_km
            })

        # 使用plotly创建交互式地图
        fig = go.Figure()

        # 添加场站点
        station_data = [d for d in map_data if d['type'] == 'station']
        fig.add_trace(go.Scattermapbox(
            lat=[d['lat'] for d in station_data],
            lon=[d['lon'] for d in station_data],
            mode='markers',
            marker=dict(
                size=[d['size'] for d in station_data],
                color='blue',
                opacity=0.7
            ),
            text=[f"{d['name']}<br>设备: {d['devices']}台" for d in station_data],
            name='充电站'
        ))

        # 添加调度路线
        route_data = [d for d in map_data if d['type'] == 'route']
        for route in route_data:
            fig.add_trace(go.Scattermapbox(
                lat=route['lat'],
                lon=route['lon'],
                mode='lines+markers',
                line=dict(width=3, color='red'),
                marker=dict(size=8, color='red'),
                text=f"{route['name']}<br>净收益: ¥{route['benefit']:,.0f}<br>距离: {route['distance']:.1f}km",
                name=route['name']
            ))

        fig.update_layout(
            mapbox=dict(
                style="open-street-map",
                center=dict(lat=31.8, lon=117.2),  # 以合肥为中心
                zoom=6
            ),
            title="移动充电车调度优化方案地图",
            height=600
        )

        fig.write_html("scheduling_map.html")
        print("交互式地图已保存为 scheduling_map.html")

    def plot_advanced_analysis(self, results: List[AdvancedSchedulingResult]) -> None:
        """绘制高级分析图表"""
        if not results:
            print("没有调度结果可以分析")
            return

        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # ROI vs 风险评分散点图
        rois = [r.roi for r in results]
        risks = [r.risk_score for r in results]
        benefits = [r.net_benefit for r in results]

        scatter = ax1.scatter(risks, rois, c=benefits, s=60, alpha=0.7, cmap='viridis')
        ax1.set_xlabel('风险评分')
        ax1.set_ylabel('投资回报率')
        ax1.set_title('风险 vs 回报分析')
        ax1.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax1, label='净收益 (元)')

        # 回本天数分布
        payback_days = [r.payback_days for r in results if r.payback_days != float('inf')]
        ax2.hist(payback_days, bins=15, alpha=0.7, color='orange', edgecolor='black')
        ax2.set_xlabel('回本天数')
        ax2.set_ylabel('方案数量')
        ax2.set_title('投资回本期分布')
        ax2.grid(True, alpha=0.3)

        # 距离 vs 净收益
        distances = [r.distance_km for r in results]
        ax3.scatter(distances, benefits, alpha=0.6, s=50, color='green')
        ax3.set_xlabel('调度距离 (km)')
        ax3.set_ylabel('净收益 (元)')
        ax3.set_title('调度距离 vs 净收益')
        ax3.grid(True, alpha=0.3)

        # 优先级评分排名
        top_10 = results[:10]
        priorities = [r.priority_score for r in top_10]
        labels = [f"{r.from_station[:10]}→{r.to_station[:10]}" for r in top_10]

        ax4.barh(range(len(priorities)), priorities, color='lightblue')
        ax4.set_yticks(range(len(labels)))
        ax4.set_yticklabels(labels, fontsize=8)
        ax4.set_xlabel('优先级评分')
        ax4.set_title('前10个方案优先级对比')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('advanced_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

def main_advanced():
    """高级调度系统主程序"""
    print("=== 高级移动充电车调度优化系统 ===\n")

    # 导入基础数据处理器
    from charging_station_scheduler import DataProcessor

    # 初始化数据处理器
    processor = DataProcessor()
    processor.load_data()
    processor.clean_and_process_data()

    # 初始化高级优化器
    optimizer = AdvancedSchedulingOptimizer(processor)

    # 设置约束条件
    constraints = SchedulingConstraints(
        max_distance_km=250,      # 最大调度距离
        min_net_benefit=1000,     # 最小净收益
        max_devices_per_move=3,   # 单次最大调度设备数
        min_devices_remain=1,     # 源站最少保留设备数
        max_total_moves=15,       # 最大调度次数
        budget_limit=30000        # 总预算限制
    )

    print("约束条件:")
    print(f"  最大调度距离: {constraints.max_distance_km} km")
    print(f"  最小净收益: ¥{constraints.min_net_benefit}")
    print(f"  单次最大调度设备数: {constraints.max_devices_per_move} 台")
    print(f"  总预算限制: ¥{constraints.budget_limit}")
    print()

    # 执行高级调度优化
    print("正在执行高级调度优化...")
    results = optimizer.optimize_with_constraints(constraints)

    # 生成报告
    report = optimizer.generate_advanced_report(results)
    print(report)

    # 保存报告
    with open('advanced_scheduling_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    print("高级调度报告已保存到 advanced_scheduling_report.txt")

    # 保存详细结果
    if results:
        results_df = pd.DataFrame([
            {
                '源场站': r.from_station,
                '目标场站': r.to_station,
                '调度设备数': r.device_count,
                '距离(km)': round(r.distance_km, 2),
                '运输成本(元)': round(r.transport_cost, 2),
                '预期月收益增长(元)': round(r.expected_revenue_increase, 2),
                '净收益(元)': round(r.net_benefit, 2),
                '投资回报率': round(r.roi, 2),
                '回本天数': round(r.payback_days, 1),
                '风险评分': round(r.risk_score, 3),
                '优先级评分': round(r.priority_score, 3)
            }
            for r in results
        ])
        results_df.to_csv('advanced_scheduling_results.csv', index=False, encoding='utf-8')
        print("详细结果已保存到 advanced_scheduling_results.csv")

    # 可视化分析
    visualizer = AdvancedVisualizer(optimizer)

    print("\n=== 生成高级可视化分析 ===")
    try:
        visualizer.create_interactive_map(results)
        visualizer.plot_advanced_analysis(results)
        print("高级可视化分析完成")
    except Exception as e:
        print(f"可视化生成失败: {e}")

    print("\n=== 高级调度优化完成 ===")

    # 输出关键指标摘要
    if results:
        total_benefit = sum(r.net_benefit for r in results)
        total_cost = sum(r.transport_cost for r in results)
        avg_roi = np.mean([r.roi for r in results])

        print(f"\n关键指标摘要:")
        print(f"  推荐调度方案数: {len(results)}")
        print(f"  总预期净收益: ¥{total_benefit:,.2f}")
        print(f"  总投资成本: ¥{total_cost:,.2f}")
        print(f"  平均投资回报率: {avg_roi:.2f}")
        print(f"  整体投资回报率: {total_benefit/total_cost:.2f}")

if __name__ == "__main__":
    main_advanced()
