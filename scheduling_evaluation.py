#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
移动充电车调度系统评估和对比分析
Evaluation and Comparison Analysis for Mobile Charging Vehicle Scheduling System
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SchedulingEvaluator:
    """调度系统评估器"""
    
    def __init__(self):
        self.basic_results = None
        self.advanced_results = None
        
    def load_results(self):
        """加载调度结果"""
        try:
            self.basic_results = pd.read_csv('scheduling_results.csv')
            print(f"加载基础调度结果: {len(self.basic_results)} 个方案")
        except FileNotFoundError:
            print("未找到基础调度结果文件")
            
        try:
            self.advanced_results = pd.read_csv('advanced_scheduling_results.csv')
            print(f"加载高级调度结果: {len(self.advanced_results)} 个方案")
        except FileNotFoundError:
            print("未找到高级调度结果文件")
            
    def compare_algorithms(self) -> Dict:
        """对比两种算法的性能"""
        comparison = {}
        
        if self.basic_results is not None:
            basic_stats = {
                '方案数量': len(self.basic_results),
                '总净收益': self.basic_results['净收益(元)'].sum(),
                '平均净收益': self.basic_results['净收益(元)'].mean(),
                '总运输成本': self.basic_results['运输成本(元)'].sum(),
                '平均距离': self.basic_results['距离(km)'].mean(),
                '总调度设备数': self.basic_results['调度设备数'].sum()
            }
            comparison['基础算法'] = basic_stats
            
        if self.advanced_results is not None:
            advanced_stats = {
                '方案数量': len(self.advanced_results),
                '总净收益': self.advanced_results['净收益(元)'].sum(),
                '平均净收益': self.advanced_results['净收益(元)'].mean(),
                '总运输成本': self.advanced_results['运输成本(元)'].sum(),
                '平均距离': self.advanced_results['距离(km)'].mean(),
                '总调度设备数': self.advanced_results['调度设备数'].sum(),
                '平均投资回报率': self.advanced_results['投资回报率'].mean(),
                '平均风险评分': self.advanced_results['风险评分'].mean(),
                '平均回本天数': self.advanced_results['回本天数'].mean()
            }
            comparison['高级算法'] = advanced_stats
            
        return comparison
        
    def calculate_efficiency_metrics(self) -> Dict:
        """计算效率指标"""
        metrics = {}
        
        if self.basic_results is not None:
            basic_efficiency = {
                '收益成本比': self.basic_results['净收益(元)'].sum() / self.basic_results['运输成本(元)'].sum(),
                '单设备平均收益': self.basic_results['净收益(元)'].sum() / self.basic_results['调度设备数'].sum(),
                '单公里收益': self.basic_results['净收益(元)'].sum() / self.basic_results['距离(km)'].sum()
            }
            metrics['基础算法'] = basic_efficiency
            
        if self.advanced_results is not None:
            advanced_efficiency = {
                '收益成本比': self.advanced_results['净收益(元)'].sum() / self.advanced_results['运输成本(元)'].sum(),
                '单设备平均收益': self.advanced_results['净收益(元)'].sum() / self.advanced_results['调度设备数'].sum(),
                '单公里收益': self.advanced_results['净收益(元)'].sum() / self.advanced_results['距离(km)'].sum(),
                '风险调整收益': self.advanced_results['净收益(元)'].sum() / (1 + self.advanced_results['风险评分'].mean())
            }
            metrics['高级算法'] = advanced_efficiency
            
        return metrics
        
    def analyze_station_patterns(self) -> Dict:
        """分析场站调度模式"""
        patterns = {}
        
        if self.advanced_results is not None:
            # 最受欢迎的源场站
            source_counts = self.advanced_results['源场站'].value_counts()
            patterns['热门源场站'] = source_counts.head(5).to_dict()
            
            # 最受欢迎的目标场站
            target_counts = self.advanced_results['目标场站'].value_counts()
            patterns['热门目标场站'] = target_counts.head(5).to_dict()
            
            # 距离分布分析
            distance_bins = pd.cut(self.advanced_results['距离(km)'], 
                                 bins=[0, 10, 50, 100, 200, float('inf')],
                                 labels=['<10km', '10-50km', '50-100km', '100-200km', '>200km'])
            patterns['距离分布'] = distance_bins.value_counts().to_dict()
            
            # 设备数量分布
            device_counts = self.advanced_results['调度设备数'].value_counts()
            patterns['设备数量分布'] = device_counts.to_dict()
            
        return patterns
        
    def generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        if self.advanced_results is not None:
            # 基于风险评分的建议
            high_risk_count = (self.advanced_results['风险评分'] > 0.3).sum()
            if high_risk_count > 0:
                recommendations.append(f"有{high_risk_count}个方案风险较高，建议优先考虑低风险方案")
                
            # 基于投资回报率的建议
            high_roi_count = (self.advanced_results['投资回报率'] > 50).sum()
            if high_roi_count > 0:
                recommendations.append(f"有{high_roi_count}个方案投资回报率超过50，建议优先实施")
                
            # 基于距离的建议
            short_distance_count = (self.advanced_results['距离(km)'] < 20).sum()
            if short_distance_count > 0:
                recommendations.append(f"有{short_distance_count}个短距离调度方案，运输成本低，建议优先考虑")
                
            # 基于回本期的建议
            quick_payback_count = (self.advanced_results['回本天数'] < 1).sum()
            if quick_payback_count > 0:
                recommendations.append(f"有{quick_payback_count}个方案可在1天内回本，投资风险极低")
                
        return recommendations
        
    def create_comparison_visualization(self):
        """创建对比可视化图表"""
        if self.basic_results is None or self.advanced_results is None:
            print("需要两种算法的结果才能进行对比")
            return
            
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 净收益对比
        basic_benefits = self.basic_results['净收益(元)'].head(10)
        advanced_benefits = self.advanced_results['净收益(元)'].head(10)
        
        x = np.arange(len(basic_benefits))
        width = 0.35
        
        ax1.bar(x - width/2, basic_benefits, width, label='基础算法', alpha=0.7)
        ax1.bar(x + width/2, advanced_benefits, width, label='高级算法', alpha=0.7)
        ax1.set_xlabel('方案排名')
        ax1.set_ylabel('净收益 (元)')
        ax1.set_title('前10个方案净收益对比')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 距离分布对比
        ax2.hist([self.basic_results['距离(km)'], self.advanced_results['距离(km)']], 
                bins=15, alpha=0.7, label=['基础算法', '高级算法'])
        ax2.set_xlabel('调度距离 (km)')
        ax2.set_ylabel('方案数量')
        ax2.set_title('调度距离分布对比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 运输成本对比
        ax3.scatter(self.basic_results['距离(km)'], self.basic_results['运输成本(元)'], 
                   alpha=0.6, label='基础算法', s=30)
        ax3.scatter(self.advanced_results['距离(km)'], self.advanced_results['运输成本(元)'], 
                   alpha=0.6, label='高级算法', s=30)
        ax3.set_xlabel('距离 (km)')
        ax3.set_ylabel('运输成本 (元)')
        ax3.set_title('距离 vs 运输成本')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 效率指标对比
        basic_efficiency = self.basic_results['净收益(元)'] / self.basic_results['运输成本(元)']
        advanced_efficiency = self.advanced_results['净收益(元)'] / self.advanced_results['运输成本(元)']
        
        ax4.boxplot([basic_efficiency, advanced_efficiency], 
                   labels=['基础算法', '高级算法'])
        ax4.set_ylabel('收益成本比')
        ax4.set_title('算法效率对比')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('algorithm_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_evaluation_report(self) -> str:
        """生成评估报告"""
        report = "=== 移动充电车调度系统评估报告 ===\n\n"
        
        # 算法对比
        comparison = self.compare_algorithms()
        report += "1. 算法性能对比\n"
        report += "=" * 50 + "\n"
        
        for algo_name, stats in comparison.items():
            report += f"\n{algo_name}:\n"
            for metric, value in stats.items():
                if isinstance(value, float):
                    report += f"  {metric}: {value:,.2f}\n"
                else:
                    report += f"  {metric}: {value:,}\n"
                    
        # 效率指标
        efficiency = self.calculate_efficiency_metrics()
        report += "\n2. 效率指标分析\n"
        report += "=" * 50 + "\n"
        
        for algo_name, metrics in efficiency.items():
            report += f"\n{algo_name}:\n"
            for metric, value in metrics.items():
                report += f"  {metric}: {value:.2f}\n"
                
        # 场站模式分析
        patterns = self.analyze_station_patterns()
        report += "\n3. 场站调度模式分析\n"
        report += "=" * 50 + "\n"
        
        for pattern_type, data in patterns.items():
            report += f"\n{pattern_type}:\n"
            for item, count in data.items():
                report += f"  {item}: {count}\n"
                
        # 优化建议
        recommendations = self.generate_recommendations()
        report += "\n4. 优化建议\n"
        report += "=" * 50 + "\n"
        
        for i, rec in enumerate(recommendations, 1):
            report += f"{i}. {rec}\n"
            
        # 总结
        report += "\n5. 总结\n"
        report += "=" * 50 + "\n"
        
        if 'high级算法' in comparison and '基础算法' in comparison:
            advanced_total = comparison['高级算法']['总净收益']
            basic_total = comparison['基础算法']['总净收益']
            improvement = ((advanced_total - basic_total) / basic_total * 100) if basic_total > 0 else 0
            
            report += f"高级算法相比基础算法，总净收益提升了 {improvement:.1f}%\n"
            report += f"高级算法通过风险控制和多目标优化，实现了更好的收益风险平衡\n"
            report += f"建议采用高级算法进行实际调度决策\n"
            
        return report

def main():
    """主程序"""
    print("=== 移动充电车调度系统评估 ===\n")
    
    evaluator = SchedulingEvaluator()
    evaluator.load_results()
    
    # 生成评估报告
    report = evaluator.generate_evaluation_report()
    print(report)
    
    # 保存报告
    with open('evaluation_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    print("评估报告已保存到 evaluation_report.txt")
    
    # 生成对比可视化
    try:
        evaluator.create_comparison_visualization()
        print("对比可视化图表已生成")
    except Exception as e:
        print(f"可视化生成失败: {e}")
    
    print("\n=== 评估完成 ===")

if __name__ == "__main__":
    main()
