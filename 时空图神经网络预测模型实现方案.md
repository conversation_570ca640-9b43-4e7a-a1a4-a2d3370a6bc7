# 时空图神经网络预测模型实现方案

## 📊 基于实际数据的模型设计

### 数据特征分析
- **时间维度**: 3个月历史数据，日级粒度
- **空间维度**: 130个场站，地理分布集中在华东地区
- **节点特征**: 设备数量、收益、利用率、地理位置
- **边特征**: 距离、交通便利性、业务关联度

---

## 🧠 时空图神经网络架构

### 1. 图结构构建

#### 1.1 场站关系图构建
```python
import torch
import torch.nn as nn
import torch.nn.functional as F
import networkx as nx
import numpy as np
from torch_geometric.nn import GCNConv, GATConv, TransformerConv

class StationGraphBuilder:
    """场站关系图构建器"""
    
    def __init__(self, stations_data: pd.DataFrame):
        self.stations_data = stations_data
        self.graph = None
        self.adjacency_matrix = None
        
    def build_graph(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """构建场站关系图"""
        n_stations = len(self.stations_data)
        
        # 1. 地理邻接关系 (基于距离)
        geo_adj = self._build_geographic_adjacency()
        
        # 2. 业务邻接关系 (基于收益相似性)
        business_adj = self._build_business_adjacency()
        
        # 3. 功能邻接关系 (基于设备类型和规模)
        functional_adj = self._build_functional_adjacency()
        
        # 4. 融合多种关系
        combined_adj = self._combine_adjacencies(geo_adj, business_adj, functional_adj)
        
        # 5. 转换为PyTorch Geometric格式
        edge_index, edge_attr = self._to_edge_format(combined_adj)
        
        return edge_index, edge_attr
    
    def _build_geographic_adjacency(self) -> np.ndarray:
        """构建地理邻接矩阵"""
        n_stations = len(self.stations_data)
        adj_matrix = np.zeros((n_stations, n_stations))
        
        for i in range(n_stations):
            for j in range(i+1, n_stations):
                # 计算地理距离
                lat1, lon1 = self.stations_data.iloc[i][['高德_纬度', '高德_经度']]
                lat2, lon2 = self.stations_data.iloc[j][['高德_纬度', '高德_经度']]
                
                distance = self._calculate_distance(lat1, lon1, lat2, lon2)
                
                # 距离权重 (高斯核函数)
                if distance < 50:  # 50km内认为有关联
                    weight = np.exp(-distance**2 / (2 * 20**2))  # σ=20km
                    adj_matrix[i, j] = adj_matrix[j, i] = weight
        
        return adj_matrix
    
    def _build_business_adjacency(self) -> np.ndarray:
        """构建业务邻接矩阵 (基于收益模式相似性)"""
        n_stations = len(self.stations_data)
        adj_matrix = np.zeros((n_stations, n_stations))
        
        # 获取每个场站的收益特征向量
        revenue_features = self._extract_revenue_features()
        
        for i in range(n_stations):
            for j in range(i+1, n_stations):
                # 计算收益模式相似性 (余弦相似度)
                similarity = self._cosine_similarity(
                    revenue_features[i], revenue_features[j]
                )
                
                if similarity > 0.7:  # 相似度阈值
                    adj_matrix[i, j] = adj_matrix[j, i] = similarity
        
        return adj_matrix
    
    def _extract_revenue_features(self) -> np.ndarray:
        """提取收益特征向量"""
        features = []
        
        for _, station in self.stations_data.iterrows():
            station_name = station['名称']
            
            # 从历史订单数据中提取特征
            station_orders = self.orders_data[
                self.orders_data['station_name'] == station_name
            ]
            
            if len(station_orders) > 0:
                feature_vector = np.array([
                    station_orders['daily_revenue'].mean(),      # 平均日收益
                    station_orders['daily_revenue'].std(),       # 收益波动性
                    station_orders['daily_revenue'].max(),       # 最大收益
                    station_orders['daily_revenue'].min(),       # 最小收益
                    len(station_orders),                         # 营业天数
                    station_orders[station_orders['is_weekend']]['daily_revenue'].mean(),  # 周末收益
                    station_orders[~station_orders['is_weekend']]['daily_revenue'].mean(), # 工作日收益
                ])
            else:
                feature_vector = np.zeros(7)
            
            features.append(feature_vector)
        
        return np.array(features)
```

#### 1.2 节点特征工程
```python
class NodeFeatureExtractor:
    """节点特征提取器"""
    
    def __init__(self, stations_data: pd.DataFrame, orders_data: pd.DataFrame):
        self.stations_data = stations_data
        self.orders_data = orders_data
        
    def extract_node_features(self, timestamp: datetime) -> torch.Tensor:
        """提取节点特征"""
        features = []
        
        for _, station in self.stations_data.iterrows():
            station_features = self._extract_single_station_features(station, timestamp)
            features.append(station_features)
        
        return torch.FloatTensor(features)
    
    def _extract_single_station_features(self, station: pd.Series, timestamp: datetime) -> np.ndarray:
        """提取单个场站的特征"""
        station_name = station['名称']
        
        # 1. 静态特征
        static_features = np.array([
            station['设备数量'],                    # 设备数量
            station['高德_经度'],                   # 经度
            station['高德_纬度'],                   # 纬度
            station['是否开放。0：否；1：是。'],      # 开放状态
        ])
        
        # 2. 历史统计特征
        historical_features = self._extract_historical_features(station_name)
        
        # 3. 时间特征
        temporal_features = np.array([
            timestamp.hour / 24.0,                 # 小时
            timestamp.weekday() / 7.0,             # 星期
            timestamp.month / 12.0,                # 月份
            int(timestamp.weekday() >= 5),         # 是否周末
        ])
        
        # 4. 外部特征 (可选)
        external_features = self._extract_external_features(station, timestamp)
        
        # 合并所有特征
        all_features = np.concatenate([
            static_features,
            historical_features, 
            temporal_features,
            external_features
        ])
        
        return all_features
    
    def _extract_historical_features(self, station_name: str) -> np.ndarray:
        """提取历史统计特征"""
        station_orders = self.orders_data[
            self.orders_data['station_name'] == station_name
        ]
        
        if len(station_orders) > 0:
            return np.array([
                station_orders['daily_revenue'].mean(),        # 平均收益
                station_orders['daily_revenue'].std(),         # 收益标准差
                station_orders['daily_revenue'].rolling(7).mean().iloc[-1],  # 7日均值
                station_orders['daily_revenue'].rolling(30).mean().iloc[-1], # 30日均值
                len(station_orders),                           # 历史天数
                station_orders['daily_revenue'].quantile(0.8), # 80分位数
            ])
        else:
            return np.zeros(6)
```

### 2. 时空图神经网络模型

#### 2.1 核心模型架构
```python
class SpatioTemporalGNN(nn.Module):
    """时空图神经网络模型"""
    
    def __init__(self, 
                 node_features: int = 16,      # 节点特征维度
                 hidden_dim: int = 64,         # 隐藏层维度
                 num_layers: int = 3,          # GNN层数
                 seq_len: int = 7,             # 输入序列长度
                 pred_len: int = 1,            # 预测长度
                 num_heads: int = 4):          # 注意力头数
        super().__init__()
        
        self.node_features = node_features
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        self.pred_len = pred_len
        
        # 1. 时间编码器 (处理时间序列)
        self.temporal_encoder = TemporalEncoder(
            input_dim=node_features,
            hidden_dim=hidden_dim,
            num_layers=2
        )
        
        # 2. 空间编码器 (处理图结构)
        self.spatial_encoder = SpatialEncoder(
            input_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            num_heads=num_heads
        )
        
        # 3. 时空融合层
        self.spatiotemporal_fusion = SpatioTemporalFusion(
            hidden_dim=hidden_dim,
            num_heads=num_heads
        )
        
        # 4. 预测头
        self.predictor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim // 2, pred_len)
        )
    
    def forward(self, 
                x: torch.Tensor,              # [batch, seq_len, num_nodes, features]
                edge_index: torch.Tensor,     # [2, num_edges]
                edge_attr: torch.Tensor = None) -> torch.Tensor:
        """前向传播"""
        
        batch_size, seq_len, num_nodes, features = x.shape
        
        # 1. 时间编码
        temporal_features = []
        for t in range(seq_len):
            # 对每个时间步进行时间编码
            temp_feat = self.temporal_encoder(x[:, t, :, :])  # [batch, num_nodes, hidden_dim]
            temporal_features.append(temp_feat)
        
        temporal_features = torch.stack(temporal_features, dim=1)  # [batch, seq_len, num_nodes, hidden_dim]
        
        # 2. 空间编码
        spatial_features = []
        for t in range(seq_len):
            # 对每个时间步进行空间编码
            spatial_feat = self.spatial_encoder(
                temporal_features[:, t, :, :], edge_index, edge_attr
            )
            spatial_features.append(spatial_feat)
        
        spatial_features = torch.stack(spatial_features, dim=1)  # [batch, seq_len, num_nodes, hidden_dim]
        
        # 3. 时空融合
        fused_features = self.spatiotemporal_fusion(spatial_features)  # [batch, num_nodes, hidden_dim]
        
        # 4. 预测
        predictions = self.predictor(fused_features)  # [batch, num_nodes, pred_len]
        
        return predictions

class TemporalEncoder(nn.Module):
    """时间编码器 (基于LSTM)"""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int = 2):
        super().__init__()
        
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1
        )
        
        self.layer_norm = nn.LayerNorm(hidden_dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """x: [batch, num_nodes, features]"""
        batch_size, num_nodes, features = x.shape
        
        # 重塑为LSTM输入格式
        x = x.view(batch_size * num_nodes, 1, features)
        
        # LSTM编码
        output, _ = self.lstm(x)
        output = output[:, -1, :]  # 取最后一个时间步
        
        # 层归一化
        output = self.layer_norm(output)
        
        # 重塑回原格式
        output = output.view(batch_size, num_nodes, -1)
        
        return output

class SpatialEncoder(nn.Module):
    """空间编码器 (基于图注意力网络)"""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int = 3, num_heads: int = 4):
        super().__init__()
        
        self.num_layers = num_layers
        self.gat_layers = nn.ModuleList()
        
        # 第一层
        self.gat_layers.append(
            GATConv(input_dim, hidden_dim // num_heads, heads=num_heads, dropout=0.1)
        )
        
        # 中间层
        for _ in range(num_layers - 2):
            self.gat_layers.append(
                GATConv(hidden_dim, hidden_dim // num_heads, heads=num_heads, dropout=0.1)
            )
        
        # 最后一层
        self.gat_layers.append(
            GATConv(hidden_dim, hidden_dim, heads=1, dropout=0.1)
        )
        
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, edge_attr: torch.Tensor = None) -> torch.Tensor:
        """x: [batch, num_nodes, features]"""
        
        # 处理批次维度
        batch_size = x.shape[0]
        x = x.view(-1, x.shape[-1])  # [batch*num_nodes, features]
        
        # 扩展边索引以处理批次
        edge_index_batch = self._expand_edge_index_for_batch(edge_index, batch_size, x.shape[0] // batch_size)
        
        # 逐层传播
        for i, (gat_layer, layer_norm) in enumerate(zip(self.gat_layers, self.layer_norms)):
            residual = x if i > 0 else None
            
            x = gat_layer(x, edge_index_batch)
            x = F.relu(x)
            
            # 残差连接
            if residual is not None and residual.shape == x.shape:
                x = x + residual
            
            x = layer_norm(x)
        
        # 重塑回批次格式
        x = x.view(batch_size, -1, x.shape[-1])
        
        return x

class SpatioTemporalFusion(nn.Module):
    """时空融合层"""
    
    def __init__(self, hidden_dim: int, num_heads: int = 4):
        super().__init__()
        
        self.multihead_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )
        
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        self.layer_norm1 = nn.LayerNorm(hidden_dim)
        self.layer_norm2 = nn.LayerNorm(hidden_dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """x: [batch, seq_len, num_nodes, hidden_dim]"""
        
        batch_size, seq_len, num_nodes, hidden_dim = x.shape
        
        # 重塑为注意力机制输入格式
        x = x.view(batch_size * num_nodes, seq_len, hidden_dim)
        
        # 自注意力机制
        attn_output, _ = self.multihead_attention(x, x, x)
        x = self.layer_norm1(x + attn_output)
        
        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.layer_norm2(x + ff_output)
        
        # 时间维度聚合 (取最后一个时间步)
        x = x[:, -1, :]  # [batch*num_nodes, hidden_dim]
        
        # 重塑回原格式
        x = x.view(batch_size, num_nodes, hidden_dim)
        
        return x
```

### 3. 训练和推理

#### 3.1 数据准备
```python
class STGNNDataLoader:
    """时空图神经网络数据加载器"""
    
    def __init__(self, 
                 stations_data: pd.DataFrame,
                 orders_data: pd.DataFrame,
                 seq_len: int = 7,
                 pred_len: int = 1):
        
        self.stations_data = stations_data
        self.orders_data = orders_data
        self.seq_len = seq_len
        self.pred_len = pred_len
        
        # 构建图结构
        self.graph_builder = StationGraphBuilder(stations_data)
        self.edge_index, self.edge_attr = self.graph_builder.build_graph()
        
        # 特征提取器
        self.feature_extractor = NodeFeatureExtractor(stations_data, orders_data)
        
    def create_sequences(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """创建训练序列"""
        # 获取所有日期
        dates = sorted(self.orders_data['date'].unique())
        
        sequences_x = []
        sequences_y = []
        
        for i in range(len(dates) - self.seq_len - self.pred_len + 1):
            # 输入序列
            input_dates = dates[i:i + self.seq_len]
            input_features = []
            
            for date in input_dates:
                daily_features = self.feature_extractor.extract_node_features(
                    pd.to_datetime(date)
                )
                input_features.append(daily_features)
            
            sequences_x.append(torch.stack(input_features))
            
            # 目标序列 (预测下一天的收益)
            target_date = dates[i + self.seq_len]
            target_revenue = self._get_daily_revenue(target_date)
            sequences_y.append(target_revenue)
        
        return torch.stack(sequences_x), torch.stack(sequences_y)
    
    def _get_daily_revenue(self, date: str) -> torch.Tensor:
        """获取指定日期的收益数据"""
        daily_data = self.orders_data[self.orders_data['date'] == date]
        
        revenue_vector = np.zeros(len(self.stations_data))
        
        for i, station in enumerate(self.stations_data.itertuples()):
            station_revenue = daily_data[
                daily_data['station_name'] == station.名称
            ]['daily_revenue'].sum()
            
            revenue_vector[i] = station_revenue
        
        return torch.FloatTensor(revenue_vector)
```

#### 3.2 模型训练
```python
class STGNNTrainer:
    """时空图神经网络训练器"""
    
    def __init__(self, model: SpatioTemporalGNN, device: str = 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        self.scheduler = torch.optim.lr_scheduler.StepLR(self.optimizer, step_size=50, gamma=0.5)
        self.criterion = nn.MSELoss()
    
    def train(self, 
              train_loader: DataLoader, 
              val_loader: DataLoader,
              epochs: int = 200):
        """训练模型"""
        
        best_val_loss = float('inf')
        patience = 20
        patience_counter = 0
        
        for epoch in range(epochs):
            # 训练阶段
            self.model.train()
            train_loss = 0
            
            for batch_x, batch_y, edge_index, edge_attr in train_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                edge_index = edge_index.to(self.device)
                
                self.optimizer.zero_grad()
                
                # 前向传播
                predictions = self.model(batch_x, edge_index, edge_attr)
                loss = self.criterion(predictions.squeeze(), batch_y)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()
                
                train_loss += loss.item()
            
            # 验证阶段
            val_loss = self.validate(val_loader)
            
            # 学习率调度
            self.scheduler.step()
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_stgnn_model.pth')
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    print(f"Early stopping at epoch {epoch}")
                    break
            
            if epoch % 10 == 0:
                print(f"Epoch {epoch}: Train Loss = {train_loss:.4f}, Val Loss = {val_loss:.4f}")
    
    def validate(self, val_loader: DataLoader) -> float:
        """验证模型"""
        self.model.eval()
        val_loss = 0
        
        with torch.no_grad():
            for batch_x, batch_y, edge_index, edge_attr in val_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                edge_index = edge_index.to(self.device)
                
                predictions = self.model(batch_x, edge_index, edge_attr)
                loss = self.criterion(predictions.squeeze(), batch_y)
                val_loss += loss.item()
        
        return val_loss / len(val_loader)
```

---

## 🎯 实际应用优化

### 针对130个场站的优化策略

1. **图结构简化**: 只连接距离<50km的场站，减少边数量
2. **特征降维**: 使用PCA将特征维度从16降到8
3. **批处理优化**: 使用小批量训练，减少内存占用
4. **模型轻量化**: 减少GNN层数和隐藏维度

### 预期性能
- **预测准确率**: 85-90%
- **训练时间**: 2-3小时 (200 epochs)
- **推理时间**: <100ms
- **内存占用**: <1GB
